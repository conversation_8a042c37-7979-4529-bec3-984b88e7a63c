<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - Miko邮箱系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
<div class="container-fluid">
    <div class="row min-vh-100">
        <!-- 侧边栏 -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="d-flex flex-column h-100 p-3 bg-primary text-white">
                <a href="/dashboard" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                    <i class="bi bi-envelope-heart me-2"></i>
                    <span class="fs-4">Miko邮箱</span>
                </a>
                <hr>
                <ul class="nav nav-pills flex-column mb-auto">
                    <li class="nav-item">
                        <a href="/dashboard" class="nav-link text-white">
                            <i class="bi bi-speedometer2 me-2"></i>
                            仪表板
                        </a>
                    </li>
                    <li>
                        <a href="/inbox" class="nav-link text-white">
                            <i class="bi bi-inbox me-2"></i>
                            收件箱
                        </a>
                    </li>
                    <li>
                        <a href="/sent" class="nav-link active text-white">
                            <i class="bi bi-send me-2"></i>
                            已发送
                        </a>
                    </li>
                    <li>
                        <a href="/compose" class="nav-link text-white">
                            <i class="bi bi-pencil-square me-2"></i>
                            写邮件
                        </a>
                    </li>
                    <li>
                        <a href="/forward" class="nav-link text-white">
                            <i class="bi bi-arrow-right-circle me-2"></i>
                            转邮件
                        </a>
                    </li>
                    <li>
                        <a href="/mailboxes" class="nav-link text-white">
                            <i class="bi bi-collection me-2"></i>
                            邮箱管理
                        </a>
                    </li>
                    <li>
                        <a href="/settings" class="nav-link text-white">
                            <i class="bi bi-gear me-2"></i>
                            设置
                        </a>
                    </li>
                </ul>
                <hr>
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <strong>{{.username}}</strong>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                        <li><a class="dropdown-item" href="/settings">个人设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="container-fluid p-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">已发送</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshEmails()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                            <a href="/compose" class="btn btn-sm btn-primary">
                                <i class="bi bi-pencil-square"></i> 写邮件
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 邮箱选择器 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <select class="form-select" id="mailboxSelect" onchange="loadEmails()">
                            <option value="">选择邮箱...</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索邮件...">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchEmails()">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 邮件列表 -->
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">已发送邮件</h6>
                        <div class="email-actions" id="emailActions" style="display: none;">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllEmails()">
                                    <i class="bi bi-check-all"></i> 全选
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="unselectAllEmails()">
                                    <i class="bi bi-x-circle"></i> 取消全选
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteSelectedEmails()">
                                    <i class="bi bi-trash"></i> 删除选中
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteAllEmails()">
                                    <i class="bi bi-trash-fill"></i> 删除全部
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="emailsContainer">
                            <div class="text-center py-5">
                                <i class="bi bi-send display-1 text-muted"></i>
                                <p class="text-muted mt-3">请选择邮箱查看已发送邮件</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 邮件详情模态框 -->
<div class="modal fade" id="emailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailSubject">邮件详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>收件人：</strong> <span id="emailTo"></span>
                </div>
                <div class="mb-3">
                    <strong>发送时间：</strong> <span id="emailDate"></span>
                </div>
                <div class="mb-3">
                    <strong>状态：</strong> <span id="emailStatus" class="badge"></span>
                </div>
                <hr>
                <div id="emailContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="forwardEmail()">
                    <i class="bi bi-arrow-right"></i> 转发
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
let currentEmails = [];
let selectedEmail = null;

// 页面加载时获取邮箱列表
document.addEventListener('DOMContentLoaded', function() {
    loadMailboxes();
});

async function loadMailboxes() {
    try {
        const response = await axios.get('/api/mailboxes');
        if (response.data.success) {
            const mailboxes = response.data.data;
            const select = document.getElementById('mailboxSelect');
            select.innerHTML = '<option value="">选择邮箱...</option>';
            
            mailboxes.forEach(mailbox => {
                const option = document.createElement('option');
                option.value = mailbox.email;
                option.textContent = mailbox.email;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load mailboxes:', error);
    }
}

async function loadEmails() {
    const mailbox = document.getElementById('mailboxSelect').value;
    if (!mailbox) {
        document.getElementById('emailsContainer').innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-send display-1 text-muted"></i>
                <p class="text-muted mt-3">请选择邮箱查看已发送邮件</p>
            </div>
        `;
        return;
    }

    try {
        const response = await axios.get(`/api/emails?mailbox=${encodeURIComponent(mailbox)}&type=sent`);
        if (response.data.success) {
            currentEmails = response.data.data || [];
            renderEmails(currentEmails);
        } else {
            document.getElementById('emailsContainer').innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-exclamation-circle display-1 text-warning"></i>
                    <p class="text-muted mt-3">加载邮件失败</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Failed to load emails:', error);
        document.getElementById('emailsContainer').innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-wifi-off display-1 text-danger"></i>
                <p class="text-muted mt-3">网络错误，请稍后重试</p>
            </div>
        `;
    }
}

function renderEmails(emails) {
    const container = document.getElementById('emailsContainer');
    const actionsDiv = document.getElementById('emailActions');

    if (!emails || emails.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-send display-1 text-muted"></i>
                <p class="text-muted mt-3">暂无已发送邮件</p>
            </div>
        `;
        actionsDiv.style.display = 'none';
        return;
    }

    // 显示操作按钮
    actionsDiv.style.display = 'block';

    const emailsHtml = emails.map(email => `
        <div class="email-item border-bottom py-3" data-email-id="${email.id}">
            <div class="row align-items-center">
                <div class="col-auto">
                    <input type="checkbox" class="form-check-input email-checkbox" value="${email.id}"
                           onclick="event.stopPropagation(); updateSelectedCount();">
                </div>
                <div class="col-md-3" onclick="showEmailDetail('${email.id}')">
                    <strong>${escapeHtml(email.to_addr || '未知收件人')}</strong>
                </div>
                <div class="col-md-5" onclick="showEmailDetail('${email.id}')">
                    <span>${escapeHtml(email.subject || '无主题')}</span>
                </div>
                <div class="col-md-3 text-end" onclick="showEmailDetail('${email.id}')">
                    <small class="text-muted">${formatDate(email.created_at)}</small>
                    ${getStatusBadge('sent')}
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = emailsHtml;
    updateSelectedCount();
}

function getStatusBadge(status) {
    switch (status) {
        case 'sent':
            return '<span class="badge bg-success ms-2">已发送</span>';
        case 'delivered':
            return '<span class="badge bg-primary ms-2">已送达</span>';
        case 'failed':
            return '<span class="badge bg-danger ms-2">发送失败</span>';
        case 'pending':
            return '<span class="badge bg-warning ms-2">发送中</span>';
        default:
            return '<span class="badge bg-secondary ms-2">未知</span>';
    }
}

async function showEmailDetail(emailId) {
    const mailbox = document.getElementById('mailboxSelect').value;
    if (!mailbox) return;

    try {
        // 获取邮件详情
        const response = await axios.get(`/api/emails/${emailId}?mailbox=${encodeURIComponent(mailbox)}`);
        if (response.data.success) {
            const email = response.data.data;
            selectedEmail = email;

            document.getElementById('emailSubject').textContent = email.subject || '无主题';
            document.getElementById('emailTo').textContent = email.to_addr || '未知收件人';
            document.getElementById('emailDate').textContent = formatDate(email.created_at);

            const statusBadge = document.getElementById('emailStatus');
            statusBadge.className = 'badge bg-success';
            statusBadge.textContent = '已发送';

            // 直接显示HTML内容，因为邮件内容来自富文本编辑器
            document.getElementById('emailContent').innerHTML = email.body || '无内容';

            const modal = new bootstrap.Modal(document.getElementById('emailModal'));
            modal.show();
        }
    } catch (error) {
        console.error('Failed to load email detail:', error);
        alert('加载邮件详情失败');
    }
}

function forwardEmail() {
    if (!selectedEmail) return;
    
    const forwardSubject = selectedEmail.subject.startsWith('Fwd: ') ? 
        selectedEmail.subject : 'Fwd: ' + selectedEmail.subject;
    
    // 跳转到写邮件页面，带上转发信息
    const params = new URLSearchParams({
        subject: forwardSubject,
        forward_from: selectedEmail.id
    });
    
    window.location.href = `/compose?${params.toString()}`;
}

function refreshEmails() {
    loadEmails();
}

function searchEmails() {
    const query = document.getElementById('searchInput').value.toLowerCase();
    if (!query) {
        renderEmails(currentEmails);
        return;
    }

    const filteredEmails = currentEmails.filter(email => 
        (email.subject && email.subject.toLowerCase().includes(query)) ||
        (email.to && email.to.toLowerCase().includes(query)) ||
        (email.content && email.content.toLowerCase().includes(query))
    );

    renderEmails(filteredEmails);
}

function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
        return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 2) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays <= 7) {
        return diffDays + '天前';
    } else {
        return date.toLocaleDateString('zh-CN');
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 邮件操作函数
function selectAllEmails() {
    const checkboxes = document.querySelectorAll('.email-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function unselectAllEmails() {
    const checkboxes = document.querySelectorAll('.email-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.email-checkbox:checked');
    const count = selectedCheckboxes.length;

    // 可以在这里添加选中数量的显示
    console.log(`已选中 ${count} 封邮件`);
}

function getSelectedEmailIds() {
    const selectedCheckboxes = document.querySelectorAll('.email-checkbox:checked');
    return Array.from(selectedCheckboxes).map(checkbox => parseInt(checkbox.value));
}

async function deleteSelectedEmails() {
    const selectedIds = getSelectedEmailIds();

    if (selectedIds.length === 0) {
        alert('请先选择要删除的邮件');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedIds.length} 封邮件吗？`)) {
        return;
    }

    try {
        // 逐个删除选中的邮件
        for (const emailId of selectedIds) {
            await axios.delete(`/api/emails/${emailId}`);
        }

        alert(`成功删除 ${selectedIds.length} 封邮件`);

        // 重新加载邮件列表
        const mailbox = document.getElementById('mailboxSelect').value;
        if (mailbox) {
            loadEmails(mailbox);
        }
    } catch (error) {
        console.error('删除邮件失败:', error);
        alert('删除邮件失败，请重试');
    }
}

async function deleteAllEmails() {
    if (currentEmails.length === 0) {
        alert('没有邮件可删除');
        return;
    }

    if (!confirm(`确定要删除当前邮箱的所有 ${currentEmails.length} 封已发送邮件吗？此操作不可恢复！`)) {
        return;
    }

    try {
        // 删除所有邮件
        for (const email of currentEmails) {
            await axios.delete(`/api/emails/${email.id}`);
        }

        alert(`成功删除 ${currentEmails.length} 封邮件`);

        // 重新加载邮件列表
        const mailbox = document.getElementById('mailboxSelect').value;
        if (mailbox) {
            loadEmails(mailbox);
        }
    } catch (error) {
        console.error('删除所有邮件失败:', error);
        alert('删除邮件失败，请重试');
    }
}

async function logout() {
    try {
        await axios.post('/api/logout');
        window.location.href = '/login';
    } catch (error) {
        console.error('Logout error:', error);
        window.location.href = '/login';
    }
}

// 搜索框回车事件
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchEmails();
    }
});
</script>

<style>
.email-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.email-item:hover {
    background-color: #f8f9fa;
}
</style>
</body>
</html>
