<template>
  <div class="dashboard">
    <!-- 警告区域 -->
    <div v-if="systemAlert" class="alert">
      <font-awesome-icon icon="exclamation-triangle" class="alert-icon" />
      <div class="alert-content">
        <h4>{{ systemAlert.title }}</h4>
        <p>{{ systemAlert.message }}</p>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div
        v-for="stat in stats"
        :key="stat.key"
        class="stat-card"
      >
        <div :class="['stat-icon', stat.iconClass]">
          <font-awesome-icon :icon="stat.icon" />
        </div>
        <div class="stat-info">
          <div class="stat-title">{{ stat.label }}</div>
          <div class="stat-value">{{ stat.value }}</div>
          <div :class="['stat-trend', stat.trend > 0 ? 'trend-up' : 'trend-down']">
            <font-awesome-icon :icon="stat.trend > 0 ? 'trending-up' : 'trending-down'" class="trend-icon" />
            <span>{{ Math.abs(stat.trend) }}% {{ stat.trend > 0 ? '增长' : '下降' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表和表格区域 -->
    <div class="card-grid">
      <div class="card">
        <div class="card-header">
          <div class="card-title">邮件概览</div>
          <div class="card-action" @click="viewEmailReport">查看报告</div>
        </div>
        <div class="chart-container">
          <!-- 图表占位 -->
          <div class="chart-placeholder">
            <div class="chart-placeholder-content">
              <font-awesome-icon icon="chart-bar" class="chart-placeholder-icon" />
              <p>邮件数据图表</p>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <div class="card-title">最近活动</div>
          <div class="card-action" @click="viewAllActivities">查看全部</div>
        </div>
        <div class="table-responsive">
          <table>
            <thead>
              <tr>
                <th>用户</th>
                <th>活动</th>
                <th>状态</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="activity in recentActivities" :key="activity.id">
                <td>{{ activity.user }}</td>
                <td>{{ activity.action }}</td>
                <td>
                  <span :class="['status', `status-${activity.status}`]">
                    {{ getStatusText(activity.status) }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

      <!-- 快速操作 -->
      <div class="space-y-6">
        <!-- 快速操作卡片 -->
        <div class="glass-card p-6">
          <h2 class="text-xl font-semibold text-white mb-6">快速操作</h2>
          <div class="space-y-3">
            <button
              v-for="action in quickActions"
              :key="action.key"
              @click="handleQuickAction(action.key)"
              class="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-slate-700/50 transition-colors text-left"
            >
              <div :class="action.iconBg" class="w-10 h-10 rounded-lg flex items-center justify-center">
                <font-awesome-icon :icon="action.icon" class="w-5 h-5 text-white" />
              </div>
              <div>
                <p class="text-white font-medium">{{ action.label }}</p>
                <p class="text-slate-400 text-sm">{{ action.description }}</p>
              </div>
            </button>
          </div>
        </div>

        <!-- 存储使用情况 -->
        <div class="glass-card p-6">
          <h2 class="text-xl font-semibold text-white mb-6">存储使用情况</h2>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-slate-400">已使用</span>
              <span class="text-white font-medium">{{ storageUsed }}GB / {{ storageTotal }}GB</span>
            </div>
            <div class="w-full bg-slate-700 rounded-full h-2">
              <div
                class="bg-gradient-to-r from-cyan-400 to-blue-400 h-2 rounded-full transition-all duration-500"
                :style="{ width: storagePercentage + '%' }"
              ></div>
            </div>
            <p class="text-slate-400 text-sm">{{ storageRemaining }}GB 可用空间</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
// import { ElMessage } from 'element-plus' // 已移除Element Plus

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)

// 系统警告
const systemAlert = ref({
  title: '系统维护通知',
  message: '系统将于今晚23:00-02:00进行维护升级，请提前保存您的工作。'
})

// 统计数据
const stats = ref([
  {
    key: 'emails',
    label: '总邮件数',
    value: '1,234',
    trend: 12,
    icon: 'envelope',
    iconClass: 'icon-bg-primary'
  },
  {
    key: 'unread',
    label: '未读邮件',
    value: '23',
    trend: -5,
    icon: 'envelope-open',
    iconClass: 'icon-bg-success'
  },
  {
    key: 'sent',
    label: '已发送',
    value: '567',
    trend: 8,
    icon: 'paper-plane',
    iconClass: 'icon-bg-warning'
  },
  {
    key: 'storage',
    label: '存储使用',
    value: '2.4GB',
    trend: 3,
    icon: 'hdd',
    iconClass: 'icon-bg-accent'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    user: 'John Smith',
    action: '发送了新邮件',
    status: 'active'
  },
  {
    id: 2,
    user: 'Emma Johnson',
    action: '更新了个人资料',
    status: 'pending'
  },
  {
    id: 3,
    user: 'Michael Brown',
    action: '创建了邮箱',
    status: 'warning'
  },
  {
    id: 4,
    user: 'Sarah Davis',
    action: '设置了转发规则',
    status: 'active'
  },
  {
    id: 5,
    user: 'Robert Wilson',
    action: '修改了密码',
    status: 'active'
  }
])

// 快速操作
const quickActions = ref([
  {
    key: 'compose',
    label: '写邮件',
    description: '发送新邮件',
    icon: 'edit',
    iconBg: 'bg-gradient-to-r from-cyan-500 to-blue-500'
  },
  {
    key: 'mailbox',
    label: '邮箱管理',
    description: '管理邮箱设置',
    icon: 'cog',
    iconBg: 'bg-gradient-to-r from-green-500 to-emerald-500'
  },
  {
    key: 'forward',
    label: '邮件转发',
    description: '设置转发规则',
    icon: 'share',
    iconBg: 'bg-gradient-to-r from-orange-500 to-red-500'
  }
])

// 存储信息
const storageUsed = ref(2.4)
const storageTotal = ref(10)
const storagePercentage = computed(() => (storageUsed.value / storageTotal.value) * 100)
const storageRemaining = computed(() => storageTotal.value - storageUsed.value)

// 方法
const getStatusText = (status) => {
  const statusMap = {
    active: '完成',
    pending: '进行中',
    warning: '待处理'
  }
  return statusMap[status] || status
}

const viewEmailReport = () => {
  router.push('/reports/email')
}

const viewAllActivities = () => {
  router.push('/activities')
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
/* 仪表板样式 */
.dashboard {
  max-width: 100%;
}

/* 警告区域 */
.alert {
  background: rgba(255, 107, 107, 0.15);
  border-left: 4px solid var(--accent);
  padding: 15px 20px;
  border-radius: 0 4px 4px 0;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.alert-icon {
  color: var(--accent);
  font-size: 24px;
  margin-right: 15px;
}

.alert-content h4 {
  font-size: 16px;
  margin-bottom: 5px;
  color: var(--accent);
}

.alert-content p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 28px;
}

.icon-bg-primary {
  background: rgba(0, 180, 216, 0.15);
  color: var(--primary);
}

.icon-bg-success {
  background: rgba(82, 183, 136, 0.15);
  color: var(--success);
}

.icon-bg-warning {
  background: rgba(255, 158, 0, 0.15);
  color: var(--warning);
}

.icon-bg-accent {
  background: rgba(255, 107, 107, 0.15);
  color: var(--accent);
}

.stat-info {
  flex-grow: 1;
}

.stat-title {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-primary);
}

.stat-trend {
  font-size: 13px;
  display: flex;
  align-items: center;
}

.trend-up {
  color: var(--success);
}

.trend-down {
  color: var(--accent);
}

.trend-icon {
  margin-right: 5px;
  font-size: 18px;
}

/* 卡片网格 */
.card-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.card {
  background: var(--card-bg);
  border-radius: 10px;
  padding: 25px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-action {
  color: var(--primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
}

.card-action:hover {
  color: var(--primary-light);
}

/* 图表容器 */
.chart-container {
  height: 300px;
  margin-top: 20px;
}

.chart-placeholder {
  background: linear-gradient(to bottom, var(--bg-dark), var(--card-bg));
  height: 100%;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border);
}

.chart-placeholder-content {
  text-align: center;
  color: var(--text-secondary);
}

.chart-placeholder-icon {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
}

/* 表格样式 */
.table-responsive {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  text-align: left;
  padding: 12px 15px;
  font-weight: 500;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border);
}

td {
  padding: 15px;
  border-bottom: 1px solid var(--border);
  color: var(--text-primary);
}

tr:last-child td {
  border-bottom: none;
}

.status {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
}

.status-active {
  background: rgba(82, 183, 136, 0.15);
  color: var(--success);
}

.status-pending {
  background: rgba(255, 158, 0, 0.15);
  color: var(--warning);
}

.status-warning {
  background: rgba(255, 107, 107, 0.15);
  color: var(--accent);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
