<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gradient">仪表板</h1>
        <p class="text-slate-400 mt-1">欢迎回来，{{ authStore.user?.username }}</p>
      </div>
      <button @click="refreshData" class="btn-primary">
        <font-awesome-icon icon="sync-alt" :class="{ 'animate-spin': loading }" class="mr-2" />
        刷新数据
      </button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
      <div
        v-for="stat in stats"
        :key="stat.key"
        class="glass-card p-6 hover:scale-105 transition-transform duration-200"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-slate-400 text-sm font-medium">{{ stat.label }}</p>
            <p class="text-2xl font-bold text-white mt-1">{{ stat.value }}</p>
            <p class="text-xs mt-2" :class="stat.trend > 0 ? 'text-green-400' : 'text-red-400'">
              <font-awesome-icon :icon="stat.trend > 0 ? 'arrow-up' : 'arrow-down'" class="mr-1" />
              {{ Math.abs(stat.trend) }}% 较上周
            </p>
          </div>
          <div :class="stat.iconBg" class="w-12 h-12 rounded-lg flex items-center justify-center">
            <font-awesome-icon :icon="stat.icon" class="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
      <!-- 最近邮件 -->
      <div class="xl:col-span-2">
        <div class="glass-card p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-white">最近邮件</h2>
            <router-link to="/email/inbox" class="text-cyan-400 hover:text-cyan-300 text-sm font-medium">
              查看全部
            </router-link>
          </div>
          
          <div v-if="recentEmails.length === 0" class="empty-state">
            <font-awesome-icon icon="inbox" class="w-12 h-12 text-slate-500 mb-4" />
            <p class="text-slate-400">暂无邮件</p>
          </div>
          
          <div v-else class="space-y-4">
            <div
              v-for="email in recentEmails"
              :key="email.id"
              class="flex items-center space-x-4 p-4 rounded-lg hover:bg-slate-700/30 transition-colors cursor-pointer"
              @click="viewEmail(email)"
            >
              <div class="w-10 h-10 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full flex items-center justify-center">
                <font-awesome-icon icon="envelope" class="w-5 h-5 text-white" />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-white font-medium truncate">{{ email.subject }}</p>
                <p class="text-slate-400 text-sm truncate">{{ email.sender }}</p>
              </div>
              <div class="text-right">
                <p class="text-slate-400 text-sm">{{ formatDate(email.date) }}</p>
                <div v-if="!email.read" class="w-2 h-2 bg-cyan-400 rounded-full mt-1 ml-auto"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="space-y-6">
        <!-- 快速操作卡片 -->
        <div class="glass-card p-6">
          <h2 class="text-xl font-semibold text-white mb-6">快速操作</h2>
          <div class="space-y-3">
            <button
              v-for="action in quickActions"
              :key="action.key"
              @click="handleQuickAction(action.key)"
              class="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-slate-700/50 transition-colors text-left"
            >
              <div :class="action.iconBg" class="w-10 h-10 rounded-lg flex items-center justify-center">
                <font-awesome-icon :icon="action.icon" class="w-5 h-5 text-white" />
              </div>
              <div>
                <p class="text-white font-medium">{{ action.label }}</p>
                <p class="text-slate-400 text-sm">{{ action.description }}</p>
              </div>
            </button>
          </div>
        </div>

        <!-- 存储使用情况 -->
        <div class="glass-card p-6">
          <h2 class="text-xl font-semibold text-white mb-6">存储使用情况</h2>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-slate-400">已使用</span>
              <span class="text-white font-medium">{{ storageUsed }}GB / {{ storageTotal }}GB</span>
            </div>
            <div class="w-full bg-slate-700 rounded-full h-2">
              <div 
                class="bg-gradient-to-r from-cyan-400 to-blue-400 h-2 rounded-full transition-all duration-500"
                :style="{ width: storagePercentage + '%' }"
              ></div>
            </div>
            <p class="text-slate-400 text-sm">{{ storageRemaining }}GB 可用空间</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const recentEmails = ref([])

// 统计数据
const stats = ref([
  {
    key: 'emails',
    label: '总邮件数',
    value: '1,234',
    trend: 12,
    icon: 'envelope',
    iconBg: 'bg-gradient-to-r from-blue-500 to-blue-600'
  },
  {
    key: 'unread',
    label: '未读邮件',
    value: '23',
    trend: -5,
    icon: 'envelope-open',
    iconBg: 'bg-gradient-to-r from-cyan-500 to-cyan-600'
  },
  {
    key: 'sent',
    label: '已发送',
    value: '567',
    trend: 8,
    icon: 'paper-plane',
    iconBg: 'bg-gradient-to-r from-green-500 to-green-600'
  },
  {
    key: 'storage',
    label: '存储使用',
    value: '2.4GB',
    trend: 3,
    icon: 'hdd',
    iconBg: 'bg-gradient-to-r from-purple-500 to-purple-600'
  }
])

// 快速操作
const quickActions = ref([
  {
    key: 'compose',
    label: '写邮件',
    description: '发送新邮件',
    icon: 'edit',
    iconBg: 'bg-gradient-to-r from-cyan-500 to-blue-500'
  },
  {
    key: 'mailbox',
    label: '邮箱管理',
    description: '管理邮箱设置',
    icon: 'cog',
    iconBg: 'bg-gradient-to-r from-green-500 to-emerald-500'
  },
  {
    key: 'forward',
    label: '邮件转发',
    description: '设置转发规则',
    icon: 'share',
    iconBg: 'bg-gradient-to-r from-orange-500 to-red-500'
  }
])

// 存储信息
const storageUsed = ref(2.4)
const storageTotal = ref(10)
const storagePercentage = computed(() => (storageUsed.value / storageTotal.value) * 100)
const storageRemaining = computed(() => storageTotal.value - storageUsed.value)

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const handleQuickAction = (action) => {
  switch (action) {
    case 'compose':
      router.push('/email/compose')
      break
    case 'mailbox':
      router.push('/mailbox')
      break
    case 'forward':
      router.push('/forward')
      break
  }
}

const viewEmail = (email) => {
  router.push(`/email/detail/${email.id}`)
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  // 模拟加载最近邮件
  recentEmails.value = [
    {
      id: 1,
      subject: '欢迎使用Miko邮箱系统',
      sender: '<EMAIL>',
      date: new Date(),
      read: false
    },
    {
      id: 2,
      subject: '系统维护通知',
      sender: '<EMAIL>',
      date: new Date(Date.now() - 86400000),
      read: true
    }
  ]
})
</script>
