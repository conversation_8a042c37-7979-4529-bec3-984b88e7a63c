用go语言加html加SQL Lite本地化数据库给我写一个miko无限邮箱系统【smtp:25端口】【imap:143端口】
【pop3端口:110】

下面是详细需求
一，网页包含那些功能
主页，登录注册，管理员登录【管理员创建账号密码admin,123456】，写邮件，收件箱，已发送，，邮件转发，
设置（普通用户【修改密码【个人中心】，切换邮箱【邮箱管理】，创建邮箱【邮箱管理】，批量创建邮箱【
邮箱管理】，邀请链接生成【注册成功一个就增加贡献度1】】 管理员【修改密码【个人中心】，切换邮箱【
邮箱管理】，创建邮箱【邮箱管理】，批量创建邮箱【邮箱管理】，邀请链接生成【用于邀请别人注册【注册
成功一个就增加贡献度1】，用户管理，域名管理，分配邮箱【用户管理】或者域名【给用户创建邮箱使用】用户管理】）
【管理员和普通用户都有邀请链接，使用邀请链接注册可以增加贡献度】
二，数据库相关
注意：
普通用户数据库
管理员用户数据库【两个用户用户数据库是独立的】
邮箱数据库【邮箱，绑定用户id，邮箱密码，】
邮件数据库【绑定邮箱id,已读和未读邮件标识等等】
..........
三，普通用户登录注册【管理员登录】
普通用户登录：用用户名，密码登录
普通用户注册：用户名，密码，密码确认，域名前缀，选择一个未分配的邮箱域名
管理员登录【用户，密码登录】
注意邀请注册是一个唯一的链接使用了增加贡献度吗，设置的个人中心显示贡献度
四，单词备注
主页【首页】
登录【普通用户登录，管理员登录】
注册【普通用户注册】
写邮件【发邮件给那个邮箱】
收件箱【别人发我邮箱的邮件【显示我有【创建的邮箱】的邮箱的全部邮件】】
已发送【显示我已经发给别人的邮件【显示我有【创建的邮箱】的邮箱的全部邮件】】
邮件转发【设置我的某一个邮箱收到的邮件转发给我输入的邮箱，可以批量选中转发给相同的我输入的邮箱】
设置【个人中心，邮箱管理，用户管理，域名管理】
五，邮箱服务相关
smtp 25 端口
IMAP 143 端口【用登录用户名【我网站登录账号】加我的域名邮箱【我创建的邮箱】加我的登录密码【网站登录密码】登录】
pop 110 端口
注意发件直接mx发件，就这样吧。