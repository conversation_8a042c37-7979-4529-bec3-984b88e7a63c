<template>
  <div class="admin-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <h1>管理员控制台</h1>
      <p>欢迎回来，{{ authStore.username }}！系统运行正常。</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalUsers }}</div>
              <div class="stats-label">总用户数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon domains">
              <el-icon><Globe /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalDomains }}</div>
              <div class="stats-label">域名数量</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon mailboxes">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalMailboxes }}</div>
              <div class="stats-label">邮箱数量</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon emails">
              <el-icon><Message /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalEmails }}</div>
              <div class="stats-label">邮件总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="action-card" @click="$router.push('/admin/users')">
          <div class="action-content">
            <el-icon class="action-icon"><UserFilled /></el-icon>
            <h3>用户管理</h3>
            <p>管理系统用户</p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="action-card" @click="$router.push('/admin/domains')">
          <div class="action-content">
            <el-icon class="action-icon"><Globe /></el-icon>
            <h3>域名管理</h3>
            <p>管理邮件域名</p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="action-card" @click="$router.push('/admin/mailboxes')">
          <div class="action-content">
            <el-icon class="action-icon"><Collection /></el-icon>
            <h3>邮箱管理</h3>
            <p>管理用户邮箱</p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="action-card" @click="refreshStats">
          <div class="action-content">
            <el-icon class="action-icon"><Refresh /></el-icon>
            <h3>刷新数据</h3>
            <p>更新统计信息</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="20">
      <el-col :xs="24" :md="12">
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>最近注册用户</span>
              <el-button type="primary" @click="$router.push('/admin/users')">查看全部</el-button>
            </div>
          </template>

          <div v-if="recentUsers.length === 0" class="empty-state">
            <el-empty description="暂无数据" />
          </div>

          <div v-else class="user-list">
            <div
              v-for="user in recentUsers"
              :key="user.id"
              class="user-item"
            >
              <div class="user-info">
                <div class="username">{{ user.username }}</div>
                <div class="email">{{ user.email }}</div>
              </div>
              <div class="user-meta">
                <div class="time">{{ formatTime(user.created_at) }}</div>
                <el-tag :type="user.is_active ? 'success' : 'danger'" size="small">
                  {{ user.is_active ? '正常' : '禁用' }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :md="12">
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <el-button @click="checkSystemStatus" :loading="checkingStatus">检查状态</el-button>
            </div>
          </template>

          <div class="system-status">
            <div class="status-item">
              <div class="status-label">数据库连接</div>
              <el-tag :type="systemStatus.database ? 'success' : 'danger'">
                {{ systemStatus.database ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <div class="status-label">SMTP服务</div>
              <el-tag :type="systemStatus.smtp ? 'success' : 'danger'">
                {{ systemStatus.smtp ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <div class="status-label">IMAP服务</div>
              <el-tag :type="systemStatus.imap ? 'success' : 'danger'">
                {{ systemStatus.imap ? '正常' : '异常' }}
              </el-tag>
            </div>
            
            <div class="status-item">
              <div class="status-label">POP3服务</div>
              <el-tag :type="systemStatus.pop3 ? 'success' : 'danger'">
                {{ systemStatus.pop3 ? '正常' : '异常' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'
import dayjs from 'dayjs'

const authStore = useAuthStore()

// 响应式数据
const checkingStatus = ref(false)

// 统计数据
const stats = reactive({
  totalUsers: 0,
  totalDomains: 0,
  totalMailboxes: 0,
  totalEmails: 0
})

// 最近用户
const recentUsers = ref([])

// 系统状态
const systemStatus = reactive({
  database: true,
  smtp: true,
  imap: true,
  pop3: true
})

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await api.get('/api/admin/stats')
    if (response.data.code === 200) {
      Object.assign(stats, response.data.data)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取最近用户
const fetchRecentUsers = async () => {
  try {
    const response = await api.get('/api/admin/users/recent?limit=5')
    if (response.data.code === 200) {
      recentUsers.value = response.data.data
    }
  } catch (error) {
    console.error('获取最近用户失败:', error)
  }
}

// 检查系统状态
const checkSystemStatus = async () => {
  try {
    checkingStatus.value = true
    const response = await api.get('/api/admin/system/status')
    if (response.data.code === 200) {
      Object.assign(systemStatus, response.data.data)
    }
  } catch (error) {
    console.error('检查系统状态失败:', error)
  } finally {
    checkingStatus.value = false
  }
}

// 刷新统计数据
const refreshStats = () => {
  fetchStats()
  fetchRecentUsers()
  checkSystemStatus()
}

// 格式化时间
const formatTime = (time) => {
  return dayjs(time).format('MM-DD HH:mm')
}

onMounted(() => {
  fetchStats()
  fetchRecentUsers()
  checkSystemStatus()
})
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border-radius: 12px;
}

.welcome-section h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.welcome-section p {
  font-size: 16px;
  opacity: 0.9;
}

.stats-row {
  margin-bottom: 30px;
}

.stats-card {
  height: 120px;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 15px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.users {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stats-icon.domains {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stats-icon.mailboxes {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stats-icon.emails {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  color: #666;
  font-size: 14px;
}

.quick-actions {
  margin-bottom: 30px;
}

.action-card {
  height: 140px;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-content {
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.action-icon {
  font-size: 32px;
  color: #f56565;
  margin-bottom: 8px;
}

.action-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.action-content p {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.activity-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  padding: 40px 0;
}

.user-list {
  max-height: 300px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.user-item:last-child {
  border-bottom: none;
}

.user-info {
  flex: 1;
}

.username {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.email {
  color: #666;
  font-size: 12px;
}

.user-meta {
  text-align: right;
}

.time {
  color: #999;
  font-size: 12px;
  margin-bottom: 4px;
}

.system-status {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.status-label {
  font-weight: 500;
  color: #333;
}

@media (max-width: 768px) {
  .admin-dashboard {
    padding: 10px;
  }
  
  .welcome-section h1 {
    font-size: 24px;
  }
  
  .stats-number {
    font-size: 24px;
  }
  
  .action-icon {
    font-size: 24px;
  }
}
</style>
