<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="layout-header" height="60px">
      <div class="header-content">
        <div class="header-left">
          <el-icon class="menu-icon" @click="toggleSidebar">
            <Menu />
          </el-icon>
          <h2 class="logo">
            <el-icon><Message /></el-icon>
            Miko邮箱系统
          </h2>
        </div>
        
        <div class="header-right">
          <el-button type="primary" @click="$router.push('/compose')">
            <el-icon><EditPen /></el-icon>
            写邮件
          </el-button>
          
          <el-dropdown @command="handleCommand">
            <span class="user-dropdown">
              <el-avatar :size="32">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ authStore.username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  个人设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- 主体内容 -->
    <div class="layout-main">
      <!-- 侧边栏 -->
      <el-aside class="layout-sidebar" :width="sidebarWidth">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          background-color="#001529"
          text-color="rgba(255, 255, 255, 0.65)"
          active-text-color="#fff"
          :collapse="isCollapse"
          router
        >
          <!-- 普通用户菜单 -->
          <template v-if="!authStore.isAdmin">
            <el-menu-item index="/dashboard">
              <el-icon><Odometer /></el-icon>
              <span>仪表板</span>
            </el-menu-item>
            <el-menu-item index="/inbox">
              <el-icon><Inbox /></el-icon>
              <span>收件箱</span>
            </el-menu-item>
            <el-menu-item index="/sent">
              <el-icon><Sent /></el-icon>
              <span>已发送</span>
            </el-menu-item>
            <el-menu-item index="/compose">
              <el-icon><EditPen /></el-icon>
              <span>写邮件</span>
            </el-menu-item>
            <el-menu-item index="/forward">
              <el-icon><Right /></el-icon>
              <span>邮件转发</span>
            </el-menu-item>
            <el-menu-item index="/mailboxes">
              <el-icon><Collection /></el-icon>
              <span>邮箱管理</span>
            </el-menu-item>
            <el-menu-item index="/settings">
              <el-icon><Setting /></el-icon>
              <span>设置</span>
            </el-menu-item>
          </template>

          <!-- 管理员菜单 -->
          <template v-else>
            <el-menu-item index="/admin/dashboard">
              <el-icon><Odometer /></el-icon>
              <span>管理面板</span>
            </el-menu-item>
            <el-menu-item index="/admin/users">
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/domains">
              <el-icon><Globe /></el-icon>
              <span>域名管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/mailboxes">
              <el-icon><Collection /></el-icon>
              <span>邮箱管理</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>

      <!-- 内容区域 -->
      <el-main class="layout-content">
        <router-view />
      </el-main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 侧边栏状态
const isCollapse = ref(false)
const sidebarWidth = computed(() => isCollapse.value ? '64px' : '250px')

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await authStore.logout()
        router.push('/login')
      } catch (error) {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped>
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.menu-icon {
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1890ff;
  font-size: 20px;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: #f5f5f5;
}

.username {
  color: #333;
  font-weight: 500;
}

.sidebar-menu {
  height: 100%;
  border-right: none;
}

.layout-content {
  background: #f5f5f5;
}

@media (max-width: 768px) {
  .header-left .logo {
    font-size: 16px;
  }
  
  .username {
    display: none;
  }
}
</style>
