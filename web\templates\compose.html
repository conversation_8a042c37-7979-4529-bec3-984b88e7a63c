<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - <PERSON><PERSON>邮箱系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <!-- Quill富文本编辑器 -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
</head>
<body>
<div class="container-fluid">
    <div class="row min-vh-100">
        <!-- 侧边栏 -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="d-flex flex-column h-100 p-3 bg-primary text-white">
                <a href="/dashboard" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                    <i class="bi bi-envelope-heart me-2"></i>
                    <span class="fs-4">Miko邮箱</span>
                </a>
                <hr>
                <ul class="nav nav-pills flex-column mb-auto">
                    <li class="nav-item">
                        <a href="/dashboard" class="nav-link text-white">
                            <i class="bi bi-speedometer2 me-2"></i>
                            仪表板
                        </a>
                    </li>
                    <li>
                        <a href="/inbox" class="nav-link text-white">
                            <i class="bi bi-inbox me-2"></i>
                            收件箱
                        </a>
                    </li>
                    <li>
                        <a href="/sent" class="nav-link text-white">
                            <i class="bi bi-send me-2"></i>
                            已发送
                        </a>
                    </li>
                    <li>
                        <a href="/compose" class="nav-link active text-white">
                            <i class="bi bi-pencil-square me-2"></i>
                            写邮件
                        </a>
                    </li>
                    <li>
                        <a href="/forward" class="nav-link text-white">
                            <i class="bi bi-arrow-right-circle me-2"></i>
                            转邮件
                        </a>
                    </li>
                    <li>
                        <a href="/mailboxes" class="nav-link text-white">
                            <i class="bi bi-collection me-2"></i>
                            邮箱管理
                        </a>
                    </li>
                    <li>
                        <a href="/settings" class="nav-link text-white">
                            <i class="bi bi-gear me-2"></i>
                            设置
                        </a>
                    </li>
                </ul>
                <hr>
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <strong>{{.username}}</strong>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                        <li><a class="dropdown-item" href="/settings">个人设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="container-fluid p-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">写邮件</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="saveDraft()">
                                <i class="bi bi-save"></i> 保存草稿
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearForm()">
                                <i class="bi bi-trash"></i> 清空
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 写邮件表单 -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">新邮件</h6>
                    </div>
                    <div class="card-body">
                        <form id="composeForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="fromEmail" class="form-label">发件人</label>
                                    <select class="form-select" id="fromEmail" required>
                                        <option value="">选择发件邮箱...</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="toEmail" class="form-label">收件人</label>
                                    <input type="email" class="form-control" id="toEmail" placeholder="<EMAIL>" required>
                                    <div class="form-text">多个收件人请用逗号分隔</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="ccEmail" class="form-label">抄送 (CC)</label>
                                    <input type="email" class="form-control" id="ccEmail" placeholder="<EMAIL>">
                                </div>
                                <div class="col-md-6">
                                    <label for="bccEmail" class="form-label">密送 (BCC)</label>
                                    <input type="email" class="form-control" id="bccEmail" placeholder="<EMAIL>">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="subject" class="form-label">主题</label>
                                <input type="text" class="form-control" id="subject" placeholder="邮件主题" required>
                            </div>

                            <div class="mb-3">
                                <label for="content" class="form-label">邮件内容</label>
                                <div id="content" style="height: 300px;"></div>
                                <input type="hidden" id="contentHidden" name="content">
                            </div>

                            <div class="mb-3">
                                <label for="attachments" class="form-label">附件</label>
                                <input type="file" class="form-control" id="attachments" multiple>
                                <div class="form-text">支持多个文件，单个文件最大10MB</div>
                                <div id="attachmentsList" class="mt-2"></div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="bi bi-send"></i> 发送邮件
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="saveDraft()">
                                        <i class="bi bi-save"></i> 保存草稿
                                    </button>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-outline-danger" onclick="clearForm()">
                                        <i class="bi bi-trash"></i> 清空表单
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 发送确认模态框 -->
<div class="modal fade" id="sendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认发送</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要发送这封邮件吗？</p>
                <div id="sendSummary"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmSend()">确认发送</button>
            </div>
        </div>
    </div>
</div>

<!-- 提示模态框 -->
<div class="modal fade" id="alertModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">提示</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="alertMessage">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
let attachedFiles = [];
let quill;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadMailboxes();
    initQuill();
    parseURLParams();
});

// 初始化Quill富文本编辑器
function initQuill() {
    quill = new Quill('#content', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, false] }],
                ['bold', 'italic', 'underline'],
                ['link', 'blockquote', 'code-block'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                ['clean']
            ]
        }
    });
}

// 解析URL参数（用于回复和转发）
function parseURLParams() {
    const urlParams = new URLSearchParams(window.location.search);
    
    if (urlParams.has('to')) {
        document.getElementById('toEmail').value = urlParams.get('to');
    }
    
    if (urlParams.has('subject')) {
        document.getElementById('subject').value = urlParams.get('subject');
    }
    
    if (urlParams.has('reply_to')) {
        // 处理回复逻辑
        loadReplyContent(urlParams.get('reply_to'));
    }
    
    if (urlParams.has('forward_from')) {
        // 处理转发逻辑
        loadForwardContent(urlParams.get('forward_from'));
    }
}

async function loadMailboxes() {
    try {
        const response = await axios.get('/api/mailboxes');
        if (response.data.success) {
            const mailboxes = response.data.data;
            const select = document.getElementById('fromEmail');
            select.innerHTML = '<option value="">选择发件邮箱...</option>';
            
            mailboxes.forEach(mailbox => {
                const option = document.createElement('option');
                option.value = mailbox.email;
                option.textContent = mailbox.email;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load mailboxes:', error);
    }
}

async function loadReplyContent(emailId) {
    try {
        const response = await axios.get(`/api/emails/${emailId}`);
        if (response.data.success) {
            const email = response.data.data;
            const replyContent = `
                <br><br>
                -------- 原始邮件 --------<br>
                发件人: ${email.from}<br>
                发送时间: ${new Date(email.date).toLocaleString('zh-CN')}<br>
                主题: ${email.subject}<br><br>
                ${email.content}
            `;

            quill.root.innerHTML = replyContent;
        }
    } catch (error) {
        console.error('Failed to load reply content:', error);
    }
}

async function loadForwardContent(emailId) {
    try {
        const response = await axios.get(`/api/emails/${emailId}`);
        if (response.data.success) {
            const email = response.data.data;
            const forwardContent = `
                <br><br>
                -------- 转发邮件 --------<br>
                发件人: ${email.from}<br>
                收件人: ${email.to}<br>
                发送时间: ${new Date(email.date).toLocaleString('zh-CN')}<br>
                主题: ${email.subject}<br><br>
                ${email.content}
            `;

            quill.root.innerHTML = forwardContent;
        }
    } catch (error) {
        console.error('Failed to load forward content:', error);
    }
}

// 处理附件选择
document.getElementById('attachments').addEventListener('change', function(e) {
    const files = Array.from(e.target.files);
    attachedFiles = files;

    const listContainer = document.getElementById('attachmentsList');
    listContainer.innerHTML = '';

    files.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'alert alert-info alert-dismissible fade show';
        fileItem.innerHTML = `
            <i class="bi bi-paperclip"></i> ${file.name} (${formatFileSize(file.size)})
            <button type="button" class="btn-close" onclick="removeAttachment(${index})"></button>
        `;
        listContainer.appendChild(fileItem);
    });
});

function removeAttachment(index) {
    attachedFiles.splice(index, 1);

    // 重新创建文件列表
    const listContainer = document.getElementById('attachmentsList');
    listContainer.innerHTML = '';

    attachedFiles.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'alert alert-info alert-dismissible fade show';
        fileItem.innerHTML = `
            <i class="bi bi-paperclip"></i> ${file.name} (${formatFileSize(file.size)})
            <button type="button" class="btn-close" onclick="removeAttachment(${index})"></button>
        `;
        listContainer.appendChild(fileItem);
    });
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 表单提交
document.getElementById('composeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    showSendConfirmation();
});

function showSendConfirmation() {
    const fromEmail = document.getElementById('fromEmail').value;
    const toEmail = document.getElementById('toEmail').value;
    const subject = document.getElementById('subject').value;

    const summary = `
        <strong>发件人：</strong> ${fromEmail}<br>
        <strong>收件人：</strong> ${toEmail}<br>
        <strong>主题：</strong> ${subject}<br>
        <strong>附件：</strong> ${attachedFiles.length} 个文件
    `;

    document.getElementById('sendSummary').innerHTML = summary;

    const modal = new bootstrap.Modal(document.getElementById('sendModal'));
    modal.show();
}

async function confirmSend() {
    const formData = new FormData();

    formData.append('from', document.getElementById('fromEmail').value);
    formData.append('to', document.getElementById('toEmail').value);
    formData.append('cc', document.getElementById('ccEmail').value);
    formData.append('bcc', document.getElementById('bccEmail').value);
    formData.append('subject', document.getElementById('subject').value);
    formData.append('content', quill.root.innerHTML);

    // 添加附件
    attachedFiles.forEach(file => {
        formData.append('attachments', file);
    });

    try {
        const response = await axios.post('/api/emails/send', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });

        if (response.data.success) {
            showAlert('邮件发送成功！');
            clearForm();

            // 关闭确认模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('sendModal'));
            modal.hide();

            // 3秒后跳转到已发送页面
            setTimeout(() => {
                window.location.href = '/sent';
            }, 3000);
        } else {
            showAlert(response.data.message || '邮件发送失败');
        }
    } catch (error) {
        console.error('Send email error:', error);
        if (error.response && error.response.data) {
            showAlert(error.response.data.message || '邮件发送失败');
        } else {
            showAlert('网络错误，请稍后重试');
        }
    }
}

function saveDraft() {
    // TODO: 实现保存草稿功能
    showAlert('草稿保存功能暂未实现');
}

function clearForm() {
    document.getElementById('composeForm').reset();
    quill.setContents([]);
    attachedFiles = [];
    document.getElementById('attachmentsList').innerHTML = '';
}

function showAlert(message) {
    document.getElementById('alertMessage').textContent = message;
    const modal = new bootstrap.Modal(document.getElementById('alertModal'));
    modal.show();
}

async function logout() {
    try {
        await axios.post('/api/logout');
        window.location.href = '/login';
    } catch (error) {
        console.error('Logout error:', error);
        window.location.href = '/login';
    }
}
</script>
</body>
</html>
