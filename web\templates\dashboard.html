<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - Miko邮箱系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
<div class="container-fluid">
    <div class="row min-vh-100">
        <!-- 侧边栏 -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="d-flex flex-column h-100 p-3 bg-primary text-white">
                <a href="/dashboard" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                    <i class="bi bi-envelope-heart me-2"></i>
                    <span class="fs-4">Miko邮箱</span>
                </a>
                <hr>
                <ul class="nav nav-pills flex-column mb-auto">
                    <li class="nav-item">
                        <a href="/dashboard" class="nav-link active text-white">
                            <i class="bi bi-speedometer2 me-2"></i>
                            仪表板
                        </a>
                    </li>
                    <li>
                        <a href="/inbox" class="nav-link text-white">
                            <i class="bi bi-inbox me-2"></i>
                            收件箱
                        </a>
                    </li>
                    <li>
                        <a href="/sent" class="nav-link text-white">
                            <i class="bi bi-send me-2"></i>
                            已发送
                        </a>
                    </li>
                    <li>
                        <a href="/compose" class="nav-link text-white">
                            <i class="bi bi-pencil-square me-2"></i>
                            写邮件
                        </a>
                    </li>
                    <li>
                        <a href="/forward" class="nav-link text-white">
                            <i class="bi bi-arrow-right-circle me-2"></i>
                            转邮件
                        </a>
                    </li>
                    <li>
                        <a href="/mailboxes" class="nav-link text-white">
                            <i class="bi bi-collection me-2"></i>
                            邮箱管理
                        </a>
                    </li>
                    <li>
                        <a href="/settings" class="nav-link text-white">
                            <i class="bi bi-gear me-2"></i>
                            设置
                        </a>
                    </li>
                </ul>
                <hr>
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <strong>{{.username}}</strong>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                        <li><a class="dropdown-item" href="/settings">个人设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="container-fluid p-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">欢迎回来，{{.username}}！</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="/compose" class="btn btn-sm btn-primary">
                                <i class="bi bi-plus-circle me-1"></i>
                                写邮件
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            我的邮箱</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalMailboxes">-</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-collection fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            未读邮件</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="unreadEmails">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-envelope fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            已发送</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="sentEmails">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-send fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            贡献度</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="contribution">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-star fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <a href="/compose" class="btn btn-primary btn-lg w-100">
                                            <i class="bi bi-pencil-square me-2"></i>
                                            写邮件
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="/mailboxes" class="btn btn-success btn-lg w-100">
                                            <i class="bi bi-collection me-2"></i>
                                            管理邮箱
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <a href="/inbox" class="btn btn-info btn-lg w-100">
                                            <i class="bi bi-inbox me-2"></i>
                                            查看收件箱
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <button class="btn btn-warning btn-lg w-100" onclick="refreshStats()">
                                            <i class="bi bi-arrow-clockwise me-2"></i>
                                            刷新数据
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近邮件 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">我的邮箱列表</h6>
                            </div>
                            <div class="card-body">
                                <div id="mailboxesList">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载邮箱列表...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
// 页面加载时获取数据
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
    loadMailboxes();
});

async function loadStats() {
    try {
        // 获取用户资料
        const profileResponse = await axios.get('/api/profile');
        if (profileResponse.data.success) {
            const user = profileResponse.data.data;
            document.getElementById('contribution').textContent = user.contribution || 0;
        }

        // 获取用户统计信息
        const statsResponse = await axios.get('/api/mailboxes/stats');
        if (statsResponse.data.success) {
            const stats = statsResponse.data.data;
            document.getElementById('totalMailboxes').textContent = stats.total_mailboxes || 0;
            document.getElementById('unreadEmails').textContent = stats.unread_emails || 0;
            document.getElementById('sentEmails').textContent = stats.sent_emails || 0;
        }
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
}

async function loadMailboxes() {
    try {
        const response = await axios.get('/api/mailboxes');
        if (response.data.success) {
            const mailboxes = response.data.data;
            displayMailboxes(mailboxes);
        }
    } catch (error) {
        console.error('Failed to load mailboxes:', error);
        document.getElementById('mailboxesList').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                加载邮箱列表失败，请稍后重试
            </div>
        `;
    }
}

function displayMailboxes(mailboxes) {
    const container = document.getElementById('mailboxesList');
    
    if (mailboxes.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h5 class="mt-3">还没有邮箱</h5>
                <p class="text-muted">点击下方按钮创建您的第一个邮箱</p>
                <a href="/mailboxes" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    创建邮箱
                </a>
            </div>
        `;
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-hover">';
    html += '<thead><tr><th>邮箱地址</th><th>创建时间</th><th>状态</th><th>操作</th></tr></thead><tbody>';
    
    mailboxes.forEach(mailbox => {
        const createdAt = new Date(mailbox.created_at).toLocaleString('zh-CN');
        const status = mailbox.status === 'active' ? '正常' : '禁用';
        const statusClass = mailbox.status === 'active' ? 'bg-success' : 'bg-secondary';
        html += `
            <tr>
                <td>
                    <i class="bi bi-envelope me-2"></i>
                    <strong>${mailbox.email}</strong>
                </td>
                <td>${createdAt}</td>
                <td>
                    <span class="badge ${statusClass}">${status}</span>
                </td>
                <td>
                    <a href="/inbox?mailbox=${encodeURIComponent(mailbox.email)}" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-inbox me-1"></i>
                        查看
                    </a>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

function refreshStats() {
    loadStats();
    loadMailboxes();
    
    // 显示刷新提示
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>刷新中...';
    btn.disabled = true;
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 1000);
}

async function logout() {
    try {
        await axios.post('/api/logout');
        window.location.href = '/login';
    } catch (error) {
        console.error('Logout error:', error);
        window.location.href = '/login';
    }
}
</script>
</body>
</html>
