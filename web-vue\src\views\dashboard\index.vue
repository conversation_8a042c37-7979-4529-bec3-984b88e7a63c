<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <h1>欢迎回来，{{ authStore.username }}！</h1>
      <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon inbox">
              <el-icon><Inbox /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.unreadEmails }}</div>
              <div class="stats-label">未读邮件</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon sent">
              <el-icon><Sent /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.totalEmails }}</div>
              <div class="stats-label">总邮件数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon mailbox">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.mailboxCount }}</div>
              <div class="stats-label">邮箱数量</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon contribution">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.contribution }}</div>
              <div class="stats-label">贡献度</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="action-card" @click="$router.push('/compose')">
          <div class="action-content">
            <el-icon class="action-icon"><EditPen /></el-icon>
            <h3>写邮件</h3>
            <p>发送新邮件</p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="action-card" @click="$router.push('/mailboxes')">
          <div class="action-content">
            <el-icon class="action-icon"><Plus /></el-icon>
            <h3>创建邮箱</h3>
            <p>添加新邮箱</p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="action-card" @click="$router.push('/forward')">
          <div class="action-content">
            <el-icon class="action-icon"><Right /></el-icon>
            <h3>邮件转发</h3>
            <p>设置转发规则</p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <el-card class="action-card" @click="$router.push('/settings')">
          <div class="action-content">
            <el-icon class="action-icon"><Setting /></el-icon>
            <h3>个人设置</h3>
            <p>修改个人信息</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近邮件 -->
    <el-card class="recent-emails">
      <template #header>
        <div class="card-header">
          <span>最近邮件</span>
          <el-button type="primary" @click="$router.push('/inbox')">查看全部</el-button>
        </div>
      </template>

      <div v-if="recentEmails.length === 0" class="empty-state">
        <el-empty description="暂无邮件" />
      </div>

      <div v-else class="email-list">
        <div
          v-for="email in recentEmails"
          :key="email.id"
          class="email-item"
          :class="{ unread: !email.is_read }"
          @click="viewEmail(email)"
        >
          <div class="email-header">
            <span class="sender">{{ email.sender }}</span>
            <span class="time">{{ formatTime(email.created_at) }}</span>
          </div>
          <div class="email-subject">{{ email.subject }}</div>
          <div class="email-preview">{{ email.preview }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

// 当前日期
const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'))

// 统计数据
const stats = reactive({
  unreadEmails: 0,
  totalEmails: 0,
  mailboxCount: 0,
  contribution: 0
})

// 最近邮件
const recentEmails = ref([])

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await api.get('/api/dashboard/stats')
    if (response.data.code === 0) {
      Object.assign(stats, response.data.data)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取最近邮件
const fetchRecentEmails = async () => {
  try {
    const response = await api.get('/api/emails/recent?limit=5')
    if (response.data.code === 0) {
      recentEmails.value = response.data.data
    }
  } catch (error) {
    console.error('获取最近邮件失败:', error)
  }
}

// 格式化时间
const formatTime = (time) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 查看邮件
const viewEmail = (email) => {
  router.push(`/inbox?id=${email.id}`)
}

onMounted(() => {
  fetchStats()
  fetchRecentEmails()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.welcome-section h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.welcome-section p {
  font-size: 16px;
  opacity: 0.9;
}

.stats-row {
  margin-bottom: 30px;
}

.stats-card {
  height: 120px;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 15px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.inbox {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stats-icon.sent {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stats-icon.mailbox {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stats-icon.contribution {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  color: #666;
  font-size: 14px;
}

.quick-actions {
  margin-bottom: 30px;
}

.action-card {
  height: 140px;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-content {
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.action-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 8px;
}

.action-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.action-content p {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.recent-emails {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  padding: 40px 0;
}

.email-list {
  max-height: 400px;
  overflow-y: auto;
}

.email-item {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.email-item:hover {
  background-color: #f5f5f5;
}

.email-item.unread {
  background-color: #f0f9ff;
  font-weight: 600;
}

.email-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sender {
  font-weight: 600;
  color: #333;
}

.time {
  color: #999;
  font-size: 12px;
}

.email-subject {
  color: #666;
  margin-bottom: 5px;
  font-size: 14px;
}

.email-preview {
  color: #999;
  font-size: 12px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }

  .welcome-section h1 {
    font-size: 24px;
  }

  .stats-number {
    font-size: 24px;
  }

  .action-icon {
    font-size: 24px;
  }
}
</style>
