<template>
  <div class="admin-mailboxes">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>邮箱管理</h2>
        <el-button @click="refreshMailboxes" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索邮箱..."
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 邮箱列表 -->
    <el-card class="mailboxes-list-card">
      <div v-if="loading && mailboxes.length === 0" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="mailboxes.length === 0" class="empty-state">
        <el-empty description="暂无邮箱" />
      </div>

      <div v-else>
        <el-table
          :data="mailboxes"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="email" label="邮箱地址" min-width="250">
            <template #default="{ row }">
              <div class="email-cell">
                <span class="email-address">{{ row.email }}</span>
                <el-tag v-if="row.is_default" type="primary" size="small">默认</el-tag>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="owner" label="所有者" width="150">
            <template #default="{ row }">
              {{ row.user_name || row.admin_name || '-' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="owner_type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.user_id ? 'success' : 'warning'">
                {{ row.user_id ? '用户' : '管理员' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="domain" label="域名" width="150" />
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === 'active' ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="email_count" label="邮件数量" width="100" />
          
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button
                size="small"
                :type="row.status === 'active' ? 'warning' : 'success'"
                @click="toggleStatus(row)"
              >
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteMailbox(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div v-if="total > 0" class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 批量操作工具栏 -->
    <div v-if="selectedMailboxes.length > 0" class="batch-toolbar">
      <div class="batch-info">
        已选择 {{ selectedMailboxes.length }} 个邮箱
      </div>
      <div class="batch-actions">
        <el-button @click="batchEnable">批量启用</el-button>
        <el-button @click="batchDisable">批量禁用</el-button>
        <el-button type="danger" @click="batchDelete">批量删除</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const mailboxes = ref([])
const selectedMailboxes = ref([])
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 获取邮箱列表
const fetchMailboxes = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchKeyword.value || undefined
    }
    
    const response = await api.get('/api/admin/mailboxes', { params })
    if (response.data.code === 200) {
      mailboxes.value = response.data.data.mailboxes
      total.value = response.data.data.total
    }
  } catch (error) {
    console.error('获取邮箱列表失败:', error)
    ElMessage.error('获取邮箱列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新邮箱列表
const refreshMailboxes = () => {
  fetchMailboxes()
}

// 搜索处理
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    currentPage.value = 1
    fetchMailboxes()
  }, 500)
}

// 格式化日期时间
const formatDateTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedMailboxes.value = selection
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchMailboxes()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchMailboxes()
}

// 切换状态
const toggleStatus = async (mailbox) => {
  try {
    const newStatus = mailbox.status === 'active' ? 'disabled' : 'active'
    const action = newStatus === 'active' ? '启用' : '禁用'
    
    await ElMessageBox.confirm(
      `确定要${action}邮箱 ${mailbox.email} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.put(`/api/admin/mailboxes/${mailbox.id}/status`, { status: newStatus })
    ElMessage.success(`邮箱${action}成功`)
    fetchMailboxes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新邮箱状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 删除邮箱
const deleteMailbox = async (mailbox) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除邮箱 ${mailbox.email} 吗？删除后无法恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.delete(`/api/admin/mailboxes/${mailbox.id}`)
    ElMessage.success('邮箱删除成功')
    fetchMailboxes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除邮箱失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量操作
const batchEnable = async () => {
  try {
    const ids = selectedMailboxes.value.map(m => m.id)
    await api.put('/api/admin/mailboxes/batch/enable', { ids })
    ElMessage.success('批量启用成功')
    fetchMailboxes()
  } catch (error) {
    console.error('批量启用失败:', error)
    ElMessage.error('批量启用失败')
  }
}

const batchDisable = async () => {
  try {
    const ids = selectedMailboxes.value.map(m => m.id)
    await api.put('/api/admin/mailboxes/batch/disable', { ids })
    ElMessage.success('批量禁用成功')
    fetchMailboxes()
  } catch (error) {
    console.error('批量禁用失败:', error)
    ElMessage.error('批量禁用失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedMailboxes.value.length} 个邮箱吗？删除后无法恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedMailboxes.value.map(m => m.id)
    await api.delete('/api/admin/mailboxes/batch', { data: { ids } })
    ElMessage.success('批量删除成功')
    fetchMailboxes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

onMounted(() => {
  fetchMailboxes()
})
</script>

<style scoped>
.admin-mailboxes {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.toolbar-left h2 {
  margin: 0;
  color: #333;
}

.mailboxes-list-card {
  min-height: 600px;
}

.loading-state,
.empty-state {
  padding: 40px 0;
}

.email-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.email-address {
  font-weight: 500;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.batch-toolbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 1000;
}

.batch-info {
  color: #666;
  font-size: 14px;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .admin-mailboxes {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .toolbar-right {
    width: 100%;
  }
  
  .toolbar-right .el-input {
    width: 100% !important;
  }
  
  .batch-toolbar {
    left: 10px;
    right: 10px;
    transform: none;
    flex-direction: column;
    gap: 10px;
  }
  
  .batch-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>
