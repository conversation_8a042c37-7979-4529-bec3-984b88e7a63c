<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - Miko邮箱系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
<div class="container-fluid bg-light min-vh-100 d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="bi bi-person-plus-fill text-primary" style="font-size: 3rem;"></i>
                            <h3 class="mt-3">用户注册</h3>
                            <p class="text-muted">创建您的Miko邮箱账户</p>
                        </div>

                        <form id="registerForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">用户名</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-person"></i>
                                            </span>
                                            <input type="text" class="form-control" id="username" name="username" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">邮箱地址</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-envelope"></i>
                                            </span>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">密码</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-lock"></i>
                                            </span>
                                            <input type="password" class="form-control" id="password" name="password" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="confirmPassword" class="form-label">确认密码</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-lock-fill"></i>
                                            </span>
                                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="domainPrefix" class="form-label">邮箱前缀</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-at"></i>
                                            </span>
                                            <input type="text" class="form-control" id="domainPrefix" name="domainPrefix" placeholder="例如: myname" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="domainId" class="form-label">选择域名</label>
                                        <select class="form-select" id="domainId" name="domainId" required>
                                            <option value="">请选择域名</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="inviteCode" class="form-label">邀请码 <small class="text-muted">(可选)</small></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-gift"></i>
                                    </span>
                                    <input type="text" class="form-control" id="inviteCode" name="inviteCode" placeholder="输入邀请码可获得贡献度奖励">
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>邮箱预览:</strong> <span id="emailPreview">请先填写邮箱前缀和选择域名</span>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-person-plus me-2"></i>
                                    注册账户
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-2">已有账户？</p>
                            <a href="/login" class="btn btn-outline-primary">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                立即登录
                            </a>
                        </div>

                        <div class="text-center mt-3">
                            <a href="/" class="text-muted small">
                                <i class="bi bi-house me-1"></i>
                                返回首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载提示 -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3 mb-0">正在注册...</p>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script>
let availableDomains = [];

// 页面加载时获取可用域名
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const response = await axios.get('/api/domains/available');
        if (response.data.success) {
            availableDomains = response.data.data;
            const domainSelect = document.getElementById('domainId');
            
            availableDomains.forEach(domain => {
                const option = document.createElement('option');
                option.value = domain.id;
                option.textContent = domain.name;
                domainSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load domains:', error);
        showAlert('加载域名列表失败', 'warning');
    }
});

// 更新邮箱预览
function updateEmailPreview() {
    const prefix = document.getElementById('domainPrefix').value;
    const domainId = document.getElementById('domainId').value;
    const preview = document.getElementById('emailPreview');
    
    if (prefix && domainId) {
        const domain = availableDomains.find(d => d.id == domainId);
        if (domain) {
            preview.textContent = `${prefix}@${domain.name}`;
            preview.className = 'text-success fw-bold';
        }
    } else {
        preview.textContent = '请先填写邮箱前缀和选择域名';
        preview.className = '';
    }
}

// 监听输入变化
document.getElementById('domainPrefix').addEventListener('input', updateEmailPreview);
document.getElementById('domainId').addEventListener('change', updateEmailPreview);

// 表单提交
document.getElementById('registerForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // 验证密码
    if (data.password !== data.confirmPassword) {
        showAlert('两次输入的密码不一致', 'warning');
        return;
    }
    
    if (data.password.length < 6) {
        showAlert('密码长度至少6位', 'warning');
        return;
    }
    
    // 验证邮箱前缀
    if (!/^[a-zA-Z0-9._-]+$/.test(data.domainPrefix)) {
        showAlert('邮箱前缀只能包含字母、数字、点、横线和下划线', 'warning');
        return;
    }
    
    // 显示加载提示
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    try {
        const response = await axios.post('/api/register', {
            username: data.username,
            password: data.password,
            email: data.email,
            domain_prefix: data.domainPrefix,
            domain_id: parseInt(data.domainId),
            invite_code: data.inviteCode || ''
        });
        
        if (response.data.success) {
            showAlert('注册成功！请登录您的账户', 'success');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        } else {
            showAlert(response.data.message || '注册失败', 'danger');
        }
    } catch (error) {
        console.error('Register error:', error);
        if (error.response && error.response.data) {
            showAlert(error.response.data.message || '注册失败', 'danger');
        } else {
            showAlert('网络错误，请稍后重试', 'danger');
        }
    } finally {
        loadingModal.hide();
    }
});

function showAlert(message, type) {
    // 移除现有的alert
    const existingAlert = document.querySelector('.alert:not(.alert-info)');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    // 创建新的alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到表单前面
    const form = document.getElementById('registerForm');
    form.parentNode.insertBefore(alertDiv, form);
    
    // 自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
</body>
</html>
