// 项目状态检查脚本
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 检查Vue项目状态...\n');

// 检查必要文件
const requiredFiles = [
  'package.json',
  'vite.config.js',
  'index.html',
  'src/main.js',
  'src/App.vue',
  'src/router/index.js',
  'src/stores/auth.js',
  'src/utils/api.js'
];

console.log('📁 检查必要文件:');
let allFilesExist = true;

requiredFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// 检查页面组件
const pageComponents = [
  'src/views/auth/Login.vue',
  'src/views/auth/Register.vue',
  'src/views/auth/AdminLogin.vue',
  'src/views/dashboard/index.vue',
  'src/views/email/Inbox.vue',
  'src/views/email/Sent.vue',
  'src/views/email/Compose.vue',
  'src/views/mailbox/index.vue',
  'src/views/forward/index.vue',
  'src/views/settings/index.vue'
];

console.log('\n📄 检查页面组件:');
pageComponents.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// 检查管理员组件
const adminComponents = [
  'src/views/admin/Dashboard.vue',
  'src/views/admin/Users.vue',
  'src/views/admin/Domains.vue',
  'src/views/admin/Mailboxes.vue'
];

console.log('\n👑 检查管理员组件:');
adminComponents.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// 检查package.json
console.log('\n📦 检查package.json配置:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

  console.log(`  ✅ 项目名称: ${packageJson.name}`);
  console.log(`  ✅ 版本: ${packageJson.version}`);

  const requiredDeps = ['vue', 'vue-router', 'pinia', 'element-plus', 'axios'];
  console.log('  📚 核心依赖:');
  requiredDeps.forEach(dep => {
    const exists = packageJson.dependencies && packageJson.dependencies[dep];
    console.log(`    ${exists ? '✅' : '❌'} ${dep}${exists ? ` (${packageJson.dependencies[dep]})` : ''}`);
  });

  const requiredDevDeps = ['vite', '@vitejs/plugin-vue'];
  console.log('  🛠️ 开发依赖:');
  requiredDevDeps.forEach(dep => {
    const exists = packageJson.devDependencies && packageJson.devDependencies[dep];
    console.log(`    ${exists ? '✅' : '❌'} ${dep}${exists ? ` (${packageJson.devDependencies[dep]})` : ''}`);
  });

} catch (error) {
  console.log('  ❌ package.json 读取失败');
  allFilesExist = false;
}

// 检查node_modules
console.log('\n📚 检查依赖安装:');
const nodeModulesExists = fs.existsSync('node_modules');
console.log(`  ${nodeModulesExists ? '✅' : '❌'} node_modules 目录`);

if (nodeModulesExists) {
  const coreModules = ['vue', 'vite', 'element-plus'];
  coreModules.forEach(module => {
    const exists = fs.existsSync(path.join('node_modules', module));
    console.log(`    ${exists ? '✅' : '❌'} ${module}`);
  });
} else {
  console.log('  ⚠️ 需要运行 npm install 安装依赖');
}

// 总结
console.log('\n📊 项目状态总结:');
if (allFilesExist && nodeModulesExists) {
  console.log('  🎉 项目配置完整，可以运行！');
  console.log('\n🚀 运行命令:');
  console.log('  npm run dev    # 启动开发服务器');
  console.log('  npm run build  # 构建生产版本');
} else {
  console.log('  ⚠️ 项目配置不完整，请检查上述问题');
  if (!nodeModulesExists) {
    console.log('\n💡 建议操作:');
    console.log('  1. 运行 npm install 安装依赖');
    console.log('  2. 运行 npm run dev 启动项目');
  }
}

console.log('\n📖 更多信息请查看 运行指南.md');
