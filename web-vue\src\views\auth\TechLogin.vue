<template>
  <div class="min-h-screen bg-gradient-to-br from-space-dark to-space-blue relative overflow-hidden flex items-center justify-center">
    <!-- 装饰性渐变圆形 -->
    <div class="decoration-circle-cyan animate-float"></div>
    <div class="decoration-circle-coral animate-float" style="animation-delay: 3s;"></div>
    
    <!-- 登录表单 -->
    <div class="w-full max-w-md mx-4">
      <div class="glass-card p-8">
        <!-- 头部 -->
        <div class="text-center mb-8">
          <div class="w-16 h-16 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full flex items-center justify-center mx-auto mb-4">
            <font-awesome-icon icon="envelope" class="w-8 h-8 text-white" />
          </div>
          <h2 class="text-2xl font-bold text-gradient mb-2">用户登录</h2>
          <p class="text-slate-400">欢迎使用Miko邮箱系统</p>
        </div>
        
        <!-- 表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <div class="relative">
              <font-awesome-icon icon="user" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <input
                v-model="loginForm.username"
                type="text"
                placeholder="请输入用户名"
                class="input-glass w-full pl-10"
              />
            </div>
          </el-form-item>
          
          <el-form-item prop="password">
            <div class="relative">
              <font-awesome-icon icon="lock" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                class="input-glass w-full pl-10 pr-10"
                @keyup.enter="handleLogin"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
              >
                <font-awesome-icon :icon="showPassword ? 'eye-slash' : 'eye'" class="w-4 h-4" />
              </button>
            </div>
          </el-form-item>
          
          <el-form-item>
            <button
              type="button"
              @click="handleLogin"
              :disabled="loading"
              class="btn-primary w-full py-3 text-lg font-semibold"
            >
              <font-awesome-icon v-if="loading" icon="spinner" class="animate-spin mr-2" />
              {{ loading ? '登录中...' : '登录' }}
            </button>
          </el-form-item>
        </el-form>
        
        <!-- 底部链接 -->
        <div class="text-center space-y-3 mt-6">
          <p class="text-slate-400">
            还没有账号？
            <router-link to="/register" class="text-cyan-400 hover:text-cyan-300 transition-colors font-medium">
              立即注册
            </router-link>
          </p>
          <p>
            <router-link to="/admin/login" class="text-blue-400 hover:text-blue-300 transition-colors font-medium">
              管理员登录
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const loginFormRef = ref()

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    const success = await authStore.login(loginForm.username, loginForm.password)
    if (success) {
      router.push('/dashboard')
    }
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 自定义渐变背景 */
.decoration-circle-cyan {
  background: radial-gradient(circle, rgba(34, 211, 238, 0.2) 0%, transparent 70%);
}

.decoration-circle-coral {
  background: radial-gradient(circle, rgba(248, 113, 113, 0.2) 0%, transparent 70%);
}
</style>
