<template>
  <div class="login-container">
    <!-- 登录表单 -->
    <div class="login-form-wrapper">
      <div class="card login-card">
        <!-- 头部 -->
        <div class="login-header">
          <div class="logo-container">
            <font-awesome-icon icon="envelope" class="logo-icon" />
          </div>
          <h2 class="login-title text-gradient">用户登录</h2>
          <p class="login-subtitle">欢迎使用Miko邮箱系统</p>
        </div>

        <!-- 表单 -->
        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <div class="input-wrapper">
              <font-awesome-icon icon="user" class="input-icon" />
              <input
                v-model="loginForm.username"
                type="text"
                placeholder="请输入用户名"
                class="input-glass"
                required
              />
            </div>
          </div>

          <div class="form-group">
            <div class="input-wrapper">
              <font-awesome-icon icon="lock" class="input-icon" />
              <input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                class="input-glass"
                required
                @keyup.enter="handleLogin"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="password-toggle"
              >
                <font-awesome-icon :icon="showPassword ? 'eye-slash' : 'eye'" />
              </button>
            </div>
          </div>

          <div class="form-group">
            <button
              type="submit"
              :disabled="loading"
              class="btn-primary login-btn"
            >
              <font-awesome-icon v-if="loading" icon="spinner" class="loading mr-2" />
              {{ loading ? '登录中...' : '登录' }}
            </button>
          </div>
        </form>

        <!-- 底部链接 -->
        <div class="login-footer">
          <p class="footer-text">
            还没有账号？
            <router-link to="/register" class="footer-link">
              立即注册
            </router-link>
          </p>
          <p>
            <router-link to="/admin/login" class="footer-link">
              管理员登录
            </router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
// import { ElMessage } from 'element-plus' // 已移除Element Plus

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const loginFormRef = ref()

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    alert('请输入用户名和密码')
    return
  }

  try {
    loading.value = true
    const success = await authStore.login(loginForm.username, loginForm.password)
    if (success) {
      alert('登录成功')
      const redirectPath = authStore.isAdmin ? '/admin/dashboard' : '/dashboard'
      router.push(redirectPath)
    } else {
      alert('登录失败，请检查用户名和密码')
    }
  } catch (error) {
    console.error('登录失败:', error)
    alert('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 登录容器 */
.login-container {
  min-height: 100vh;
  background-color: var(--bg-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-form-wrapper {
  width: 100%;
  max-width: 400px;
}

.login-card {
  padding: 40px;
  background: var(--card-bg);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-lg);
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo-container {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.logo-icon {
  font-size: 28px;
  color: white;
}

.login-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
}

.login-subtitle {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 表单样式 */
.login-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 16px;
  z-index: 2;
}

.input-glass {
  width: 100%;
  padding: 15px 15px 15px 45px;
  background: var(--bg-darker);
  border: 1px solid var(--border);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.3s ease;
}

.input-glass:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 180, 216, 0.2);
}

.input-glass::placeholder {
  color: var(--text-secondary);
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: color 0.3s ease;
  z-index: 2;
}

.password-toggle:hover {
  color: var(--primary);
}

.login-btn {
  width: 100%;
  padding: 15px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

/* 底部链接 */
.login-footer {
  text-align: center;
}

.footer-text {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 10px;
}

.footer-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: var(--primary-light);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }

  .login-title {
    font-size: 20px;
  }
}
</style>
