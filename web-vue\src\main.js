import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Font Awesome
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import {
  faEnvelope, faInbox, faPaperPlane, faUser, faCog, faSignOutAlt,
  faPlus, faSearch, faTrash, faEdit, faEye, faUsers, faGlobe,
  faChartBar, faServer, faHome, faBars, faTimes, faCheck,
  faExclamationTriangle, faSpinner, faDownload, faUpload,
  faLock, faEyeSlash, faChevronDown, faShare, faEnvelopeOpen,
  faHdd, faArrowUp, faArrowDown, faSyncAlt, faBell
} from '@fortawesome/free-solid-svg-icons'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'
import './styles/index.scss'
import './styles/tech-theme.css'

// 添加图标到库
library.add(
  faEnvelope, faInbox, faPaperPlane, faUser, faCog, faSignOutAlt,
  faPlus, faSearch, faTrash, faEdit, faEye, faUsers, faGlobe,
  faChartBar, faServer, faHome, faBars, faTimes, faCheck,
  faExclamationTriangle, faSpinner, faDownload, faUpload,
  faLock, faEyeSlash, faChevronDown, faShare, faEnvelopeOpen,
  faHdd, faArrowUp, faArrowDown, faSyncAlt, faBell
)

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)

// 初始化认证状态
const authStore = useAuthStore()
authStore.checkAuth()

app.use(router)

// 注册Font Awesome组件
app.component('font-awesome-icon', FontAwesomeIcon)

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.meta.requiresAdmin && !authStore.isAdmin) {
    next('/dashboard')
  } else {
    next()
  }
})

app.mount('#app')
