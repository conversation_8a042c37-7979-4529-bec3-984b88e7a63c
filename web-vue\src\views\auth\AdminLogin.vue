<template>
  <div class="admin-login-container">
    <div class="admin-login-card">
      <div class="admin-login-header">
        <h1>
          <el-icon><Setting /></el-icon>
          Mi<PERSON>邮箱系统
        </h1>
        <p>管理员登录</p>
      </div>

      <el-form
        ref="adminLoginFormRef"
        :model="adminLoginForm"
        :rules="adminLoginRules"
        class="admin-login-form"
        @submit.prevent="handleAdminLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="adminLoginForm.username"
            placeholder="请输入管理员用户名"
            size="large"
            prefix-icon="UserFilled"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="adminLoginForm.password"
            type="password"
            placeholder="请输入管理员密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleAdminLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="danger"
            size="large"
            :loading="loading"
            class="admin-login-button"
            @click="handleAdminLogin"
          >
            管理员登录
          </el-button>
        </el-form-item>
      </el-form>

      <div class="admin-login-footer">
        <router-link to="/login">普通用户登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const adminLoginFormRef = ref()

// 加载状态
const loading = ref(false)

// 管理员登录表单
const adminLoginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const adminLoginRules = {
  username: [
    { required: true, message: '请输入管理员用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入管理员密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ]
}

// 处理管理员登录
const handleAdminLogin = async () => {
  if (!adminLoginFormRef.value) return
  
  try {
    const valid = await adminLoginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    const success = await authStore.adminLogin(adminLoginForm.username, adminLoginForm.password)
    
    if (success) {
      router.push('/admin/dashboard')
    }
  } catch (error) {
    console.error('管理员登录失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  padding: 20px;
}

.admin-login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.admin-login-header {
  text-align: center;
  margin-bottom: 30px;
}

.admin-login-header h1 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #f56565;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.admin-login-header p {
  color: #666;
  font-size: 16px;
}

.admin-login-form {
  margin-bottom: 20px;
}

.admin-login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.admin-login-footer {
  text-align: center;
}

.admin-login-footer a {
  color: #f56565;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.admin-login-footer a:hover {
  color: #fc8181;
}

@media (max-width: 480px) {
  .admin-login-card {
    padding: 30px 20px;
  }
  
  .admin-login-header h1 {
    font-size: 24px;
  }
}
</style>
