<template>
  <div class="email-detail">
    <!-- 邮件头部 -->
    <div class="email-header">
      <div class="header-main">
        <h2 class="subject">{{ email.subject || '(无主题)' }}</h2>
        <div class="actions">
          <el-button @click="handleReply">
            <el-icon><Back /></el-icon>
            回复
          </el-button>
          <el-button @click="handleForward">
            <el-icon><Right /></el-icon>
            转发
          </el-button>
          <el-button 
            :type="email.is_read ? 'default' : 'primary'"
            @click="toggleReadStatus"
          >
            <el-icon><View /></el-icon>
            {{ email.is_read ? '标记未读' : '标记已读' }}
          </el-button>
          <el-button type="danger" @click="handleDelete">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
      </div>
      
      <div class="header-info">
        <div class="sender-info">
          <div class="sender">
            <strong>发件人：</strong>{{ email.sender }}
          </div>
          <div class="recipient">
            <strong>收件人：</strong>{{ email.recipient }}
          </div>
          <div v-if="email.cc" class="cc">
            <strong>抄送：</strong>{{ email.cc }}
          </div>
        </div>
        
        <div class="time-info">
          <div class="send-time">
            {{ formatDateTime(email.created_at) }}
          </div>
          <div v-if="email.has_attachment" class="attachment-info">
            <el-icon><Paperclip /></el-icon>
            包含附件
          </div>
        </div>
      </div>
    </div>

    <!-- 邮件内容 -->
    <div class="email-content">
      <div v-if="email.content_type === 'html'" class="html-content">
        <iframe
          ref="contentFrame"
          :srcdoc="email.content"
          class="content-iframe"
          sandbox="allow-same-origin"
        />
      </div>
      <div v-else class="text-content">
        <pre>{{ email.content }}</pre>
      </div>
    </div>

    <!-- 附件列表 -->
    <div v-if="email.attachments && email.attachments.length > 0" class="attachments">
      <h3>附件</h3>
      <div class="attachment-list">
        <div
          v-for="attachment in email.attachments"
          :key="attachment.id"
          class="attachment-item"
        >
          <div class="attachment-info">
            <el-icon><Document /></el-icon>
            <span class="filename">{{ attachment.filename }}</span>
            <span class="filesize">({{ formatFileSize(attachment.size) }})</span>
          </div>
          <el-button size="small" @click="downloadAttachment(attachment)">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'
import dayjs from 'dayjs'

const props = defineProps({
  email: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'delete', 'mark-read'])

const router = useRouter()
const contentFrame = ref()

// 格式化日期时间
const formatDateTime = (time) => {
  return dayjs(time).format('YYYY年MM月DD日 HH:mm:ss')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 切换已读状态
const toggleReadStatus = async () => {
  try {
    const newStatus = !props.email.is_read
    await api.put(`/api/emails/${props.email.id}/read`, { is_read: newStatus })
    emit('mark-read', props.email.id, newStatus)
    ElMessage.success(newStatus ? '已标记为已读' : '已标记为未读')
  } catch (error) {
    console.error('切换已读状态失败:', error)
    ElMessage.error('操作失败')
  }
}

// 处理回复
const handleReply = () => {
  router.push({
    path: '/compose',
    query: {
      type: 'reply',
      id: props.email.id
    }
  })
  emit('close')
}

// 处理转发
const handleForward = () => {
  router.push({
    path: '/compose',
    query: {
      type: 'forward',
      id: props.email.id
    }
  })
  emit('close')
}

// 处理删除
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除这封邮件吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.delete(`/api/emails/${props.email.id}`)
    emit('delete', props.email.id)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除邮件失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 下载附件
const downloadAttachment = async (attachment) => {
  try {
    const response = await api.get(`/api/emails/${props.email.id}/attachments/${attachment.id}`, {
      responseType: 'blob'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', attachment.filename)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载附件失败:', error)
    ElMessage.error('下载失败')
  }
}

// 调整iframe高度
const adjustIframeHeight = () => {
  if (contentFrame.value) {
    try {
      const iframe = contentFrame.value
      iframe.onload = () => {
        const doc = iframe.contentDocument || iframe.contentWindow.document
        const height = doc.body.scrollHeight
        iframe.style.height = height + 'px'
      }
    } catch (error) {
      console.error('调整iframe高度失败:', error)
    }
  }
}

onMounted(() => {
  nextTick(() => {
    if (props.email.content_type === 'html') {
      adjustIframeHeight()
    }
  })
})
</script>

<style scoped>
.email-detail {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.email-header {
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  gap: 20px;
}

.subject {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
  flex: 1;
  word-break: break-word;
}

.actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.header-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.sender-info {
  flex: 1;
}

.sender-info > div {
  margin-bottom: 5px;
  color: #666;
  font-size: 14px;
}

.sender-info strong {
  color: #333;
  margin-right: 8px;
}

.time-info {
  text-align: right;
  flex-shrink: 0;
}

.send-time {
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.attachment-info {
  color: #1890ff;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.email-content {
  margin-bottom: 30px;
}

.html-content {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.content-iframe {
  width: 100%;
  min-height: 300px;
  border: none;
  background: white;
}

.text-content {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
}

.text-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.attachments {
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.attachments h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
}

.attachment-item .attachment-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.filename {
  font-weight: 500;
  color: #333;
}

.filesize {
  color: #666;
  font-size: 12px;
}

@media (max-width: 768px) {
  .email-detail {
    padding: 15px;
  }
  
  .header-main {
    flex-direction: column;
    gap: 15px;
  }
  
  .subject {
    font-size: 20px;
  }
  
  .actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .header-info {
    flex-direction: column;
    gap: 10px;
  }
  
  .time-info {
    text-align: left;
  }
  
  .attachment-item {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
}
</style>
