<template>
  <div class="settings">
    <div class="settings-container">
      <!-- 侧边导航 -->
      <div class="settings-sidebar">
        <el-menu
          :default-active="activeTab"
          class="settings-menu"
          @select="handleTabChange"
        >
          <el-menu-item index="profile">
            <el-icon><User /></el-icon>
            <span>个人信息</span>
          </el-menu-item>
          <el-menu-item index="password">
            <el-icon><Lock /></el-icon>
            <span>修改密码</span>
          </el-menu-item>
          <el-menu-item index="invite">
            <el-icon><Share /></el-icon>
            <span>邀请链接</span>
          </el-menu-item>
          <el-menu-item index="notification">
            <el-icon><Bell /></el-icon>
            <span>通知设置</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 主内容区 -->
      <div class="settings-content">
        <!-- 个人信息 -->
        <el-card v-if="activeTab === 'profile'" class="settings-card">
          <template #header>
            <h3>个人信息</h3>
          </template>
          
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
            class="profile-form"
          >
            <el-form-item label="用户名">
              <el-input v-model="profileForm.username" disabled />
            </el-form-item>
            
            <el-form-item label="邮箱地址" prop="email">
              <el-input v-model="profileForm.email" />
            </el-form-item>
            
            <el-form-item label="贡献度">
              <el-input v-model="profileForm.contribution" disabled>
                <template #append>分</template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="注册时间">
              <el-input :value="formatDateTime(profileForm.createdAt)" disabled />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="updateProfile" :loading="updating">
                更新信息
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 修改密码 -->
        <el-card v-if="activeTab === 'password'" class="settings-card">
          <template #header>
            <h3>修改密码</h3>
          </template>
          
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
            class="password-form"
          >
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="passwordForm.currentPassword"
                type="password"
                show-password
                placeholder="请输入当前密码"
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                show-password
                placeholder="请输入新密码"
              />
            </el-form-item>
            
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                show-password
                placeholder="请再次输入新密码"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="changePassword" :loading="changingPassword">
                修改密码
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 邀请链接 -->
        <el-card v-if="activeTab === 'invite'" class="settings-card">
          <template #header>
            <h3>邀请链接</h3>
          </template>
          
          <div class="invite-section">
            <el-alert
              title="邀请奖励"
              description="成功邀请他人注册可获得贡献度奖励，贡献度可用于解锁更多功能。"
              type="info"
              :closable="false"
              class="invite-alert"
            />
            
            <div class="invite-stats">
              <div class="stat-item">
                <div class="stat-number">{{ inviteStats.totalInvites }}</div>
                <div class="stat-label">总邀请数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ inviteStats.successfulInvites }}</div>
                <div class="stat-label">成功邀请</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ inviteStats.earnedContribution }}</div>
                <div class="stat-label">获得贡献度</div>
              </div>
            </div>
            
            <div class="invite-link-section">
              <el-form-item label="邀请链接">
                <el-input
                  :value="inviteLink"
                  readonly
                  class="invite-link-input"
                >
                  <template #append>
                    <el-button @click="copyInviteLink">
                      <el-icon><DocumentCopy /></el-icon>
                      复制
                    </el-button>
                  </template>
                </el-input>
              </el-form-item>
              
              <el-form-item label="邀请码">
                <el-input
                  :value="profileForm.inviteCode"
                  readonly
                  class="invite-code-input"
                >
                  <template #append>
                    <el-button @click="copyInviteCode">
                      <el-icon><DocumentCopy /></el-icon>
                      复制
                    </el-button>
                  </template>
                </el-input>
              </el-form-item>
            </div>
          </div>
        </el-card>

        <!-- 通知设置 -->
        <el-card v-if="activeTab === 'notification'" class="settings-card">
          <template #header>
            <h3>通知设置</h3>
          </template>
          
          <el-form label-width="150px" class="notification-form">
            <el-form-item label="邮件通知">
              <el-switch
                v-model="notificationSettings.emailNotification"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="form-tip">接收新邮件时发送通知</div>
            </el-form-item>
            
            <el-form-item label="系统通知">
              <el-switch
                v-model="notificationSettings.systemNotification"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="form-tip">接收系统重要通知</div>
            </el-form-item>
            
            <el-form-item label="营销邮件">
              <el-switch
                v-model="notificationSettings.marketingEmail"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="form-tip">接收产品更新和营销信息</div>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="updateNotificationSettings" :loading="updatingNotification">
                保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'
import dayjs from 'dayjs'

const authStore = useAuthStore()

// 响应式数据
const activeTab = ref('profile')
const updating = ref(false)
const changingPassword = ref(false)
const updatingNotification = ref(false)

// 表单引用
const profileFormRef = ref()
const passwordFormRef = ref()

// 个人信息表单
const profileForm = reactive({
  username: '',
  email: '',
  contribution: 0,
  inviteCode: '',
  createdAt: ''
})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 邀请统计
const inviteStats = reactive({
  totalInvites: 0,
  successfulInvites: 0,
  earnedContribution: 0
})

// 通知设置
const notificationSettings = reactive({
  emailNotification: true,
  systemNotification: true,
  marketingEmail: false
})

// 邀请链接
const inviteLink = computed(() => {
  if (profileForm.inviteCode) {
    return `${window.location.origin}/register?invite=${profileForm.inviteCode}`
  }
  return ''
})

// 表单验证规则
const profileRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 确认密码验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 切换标签
const handleTabChange = (key) => {
  activeTab.value = key
}

// 格式化日期时间
const formatDateTime = (time) => {
  return dayjs(time).format('YYYY年MM月DD日 HH:mm:ss')
}

// 获取用户信息
const fetchUserProfile = async () => {
  try {
    const response = await api.get('/api/user/profile')
    if (response.data.code === 0) {
      const userData = response.data.data
      Object.assign(profileForm, userData)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 获取邀请统计
const fetchInviteStats = async () => {
  try {
    const response = await api.get('/api/user/invite-stats')
    if (response.data.code === 0) {
      Object.assign(inviteStats, response.data.data)
    }
  } catch (error) {
    console.error('获取邀请统计失败:', error)
  }
}

// 获取通知设置
const fetchNotificationSettings = async () => {
  try {
    const response = await api.get('/api/user/notification-settings')
    if (response.data.code === 0) {
      Object.assign(notificationSettings, response.data.data)
    }
  } catch (error) {
    console.error('获取通知设置失败:', error)
  }
}

// 更新个人信息
const updateProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    const valid = await profileFormRef.value.validate()
    if (!valid) return
    
    updating.value = true
    
    const data = {
      email: profileForm.email
    }
    
    const response = await api.put('/api/user/profile', data)
    if (response.data.code === 0) {
      ElMessage.success('个人信息更新成功')
    } else {
      ElMessage.error(response.data.msg || response.data.message || '更新失败')
    }
  } catch (error) {
    console.error('更新个人信息失败:', error)
    ElMessage.error('更新失败')
  } finally {
    updating.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    const valid = await passwordFormRef.value.validate()
    if (!valid) return
    
    changingPassword.value = true
    
    const data = {
      current_password: passwordForm.currentPassword,
      new_password: passwordForm.newPassword
    }
    
    const response = await api.put('/api/user/password', data)
    if (response.data.code === 0) {
      ElMessage.success('密码修改成功')
      // 重置表单
      passwordForm.currentPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
      passwordFormRef.value.resetFields()
    } else {
      ElMessage.error(response.data.msg || response.data.message || '修改失败')
    }
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改失败')
  } finally {
    changingPassword.value = false
  }
}

// 更新通知设置
const updateNotificationSettings = async () => {
  try {
    updatingNotification.value = true
    
    const response = await api.put('/api/user/notification-settings', notificationSettings)
    if (response.data.code === 0) {
      ElMessage.success('通知设置保存成功')
    } else {
      ElMessage.error(response.data.msg || response.data.message || '保存失败')
    }
  } catch (error) {
    console.error('保存通知设置失败:', error)
    ElMessage.error('保存失败')
  } finally {
    updatingNotification.value = false
  }
}

// 复制邀请链接
const copyInviteLink = async () => {
  try {
    await navigator.clipboard.writeText(inviteLink.value)
    ElMessage.success('邀请链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 复制邀请码
const copyInviteCode = async () => {
  try {
    await navigator.clipboard.writeText(profileForm.inviteCode)
    ElMessage.success('邀请码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

onMounted(() => {
  fetchUserProfile()
  fetchInviteStats()
  fetchNotificationSettings()
})
</script>

<style scoped>
.settings {
  padding: 20px;
}

.settings-container {
  display: flex;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.settings-sidebar {
  width: 200px;
  flex-shrink: 0;
}

.settings-menu {
  border-right: none;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-content {
  flex: 1;
}

.settings-card {
  margin-bottom: 20px;
}

.settings-card h3 {
  margin: 0;
  color: #333;
}

.profile-form,
.password-form,
.notification-form {
  max-width: 500px;
}

.invite-section {
  max-width: 600px;
}

.invite-alert {
  margin-bottom: 20px;
}

.invite-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.invite-link-section {
  max-width: 500px;
}

.invite-link-input,
.invite-code-input {
  margin-bottom: 15px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .settings {
    padding: 10px;
  }
  
  .settings-container {
    flex-direction: column;
  }
  
  .settings-sidebar {
    width: 100%;
  }
  
  .settings-menu {
    display: flex;
    overflow-x: auto;
  }
  
  .invite-stats {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
