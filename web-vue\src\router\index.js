import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import NProgress from 'nprogress'

// 路由懒加载
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')
const AdminLogin = () => import('@/views/auth/AdminLogin.vue')
const Layout = () => import('@/layout/index.vue')
const Dashboard = () => import('@/views/dashboard/index.vue')
const Inbox = () => import('@/views/email/Inbox.vue')
const Sent = () => import('@/views/email/Sent.vue')
const Compose = () => import('@/views/email/Compose.vue')
const Mailboxes = () => import('@/views/mailbox/index.vue')
const Forward = () => import('@/views/forward/index.vue')
const Settings = () => import('@/views/settings/index.vue')
const AdminDashboard = () => import('@/views/admin/Dashboard.vue')
const AdminUsers = () => import('@/views/admin/Users.vue')
const AdminDomains = () => import('@/views/admin/Domains.vue')
const AdminMailboxes = () => import('@/views/admin/Mailboxes.vue')

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { requiresGuest: true }
  },
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin,
    meta: { requiresGuest: true }
  },
  {
    path: '/dashboard',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: Dashboard
      },
      {
        path: '/inbox',
        name: 'Inbox',
        component: Inbox
      },
      {
        path: '/sent',
        name: 'Sent',
        component: Sent
      },
      {
        path: '/compose',
        name: 'Compose',
        component: Compose
      },
      {
        path: '/mailboxes',
        name: 'Mailboxes',
        component: Mailboxes
      },
      {
        path: '/forward',
        name: 'Forward',
        component: Forward
      },
      {
        path: '/settings',
        name: 'Settings',
        component: Settings
      }
    ]
  },
  {
    path: '/admin',
    component: Layout,
    meta: { requiresAuth: true, requiresAdmin: true },
    children: [
      {
        path: '',
        redirect: '/admin/dashboard'
      },
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: AdminDashboard
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: AdminUsers
      },
      {
        path: 'domains',
        name: 'AdminDomains',
        component: AdminDomains
      },
      {
        path: 'mailboxes',
        name: 'AdminMailboxes',
        component: AdminMailboxes
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const authStore = useAuthStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next('/login')
      return
    }
    
    // 检查是否需要管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      next('/dashboard')
      return
    }
  }
  
  // 检查是否需要游客状态（未登录）
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    if (authStore.isAdmin) {
      next('/admin/dashboard')
    } else {
      next('/dashboard')
    }
    return
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
