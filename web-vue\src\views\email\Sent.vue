<template>
  <div class="sent">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>已发送</h2>
        <el-button @click="refreshEmails" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索邮件..."
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 邮件列表 -->
    <el-card class="email-list-card">
      <div v-if="loading && emails.length === 0" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="emails.length === 0" class="empty-state">
        <el-empty description="暂无已发送邮件" />
      </div>

      <div v-else class="email-list">
        <div
          v-for="email in emails"
          :key="email.id"
          class="email-item"
          :class="{ selected: selectedEmail?.id === email.id }"
          @click="selectEmail(email)"
        >
          <div class="email-checkbox">
            <el-checkbox
              v-model="email.selected"
              @click.stop
              @change="handleEmailSelect"
            />
          </div>
          
          <div class="email-content">
            <div class="email-header">
              <span class="recipient">收件人: {{ email.recipient }}</span>
              <div class="email-meta">
                <span v-if="email.has_attachment" class="attachment-icon">
                  <el-icon><Paperclip /></el-icon>
                </span>
                <span class="time">{{ formatTime(email.created_at) }}</span>
              </div>
            </div>
            
            <div class="email-subject">{{ email.subject || '(无主题)' }}</div>
            <div class="email-preview">{{ email.preview }}</div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 邮件详情抽屉 -->
    <el-drawer
      v-model="showEmailDetail"
      title="邮件详情"
      size="60%"
      direction="rtl"
    >
      <EmailDetail
        v-if="selectedEmail"
        :email="selectedEmail"
        @close="showEmailDetail = false"
        @delete="handleDeleteEmail"
      />
    </el-drawer>

    <!-- 批量操作工具栏 -->
    <div v-if="selectedEmails.length > 0" class="batch-toolbar">
      <div class="batch-info">
        已选择 {{ selectedEmails.length }} 封邮件
      </div>
      <div class="batch-actions">
        <el-button type="danger" @click="deleteSelected">删除选中</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'
import dayjs from 'dayjs'
import EmailDetail from '@/components/EmailDetail.vue'

// 响应式数据
const loading = ref(false)
const emails = ref([])
const selectedEmail = ref(null)
const showEmailDetail = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 计算属性
const selectedEmails = computed(() => emails.value.filter(email => email.selected))

// 获取已发送邮件列表
const fetchEmails = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchKeyword.value || undefined
    }
    
    const response = await api.get('/api/emails/sent', { params })
    if (response.data.code === 200) {
      emails.value = response.data.data.emails.map(email => ({
        ...email,
        selected: false
      }))
      total.value = response.data.data.total
    }
  } catch (error) {
    console.error('获取已发送邮件列表失败:', error)
    ElMessage.error('获取邮件列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新邮件
const refreshEmails = () => {
  fetchEmails()
}

// 搜索处理
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    currentPage.value = 1
    fetchEmails()
  }, 500)
}

// 选择邮件
const selectEmail = (email) => {
  selectedEmail.value = email
  showEmailDetail.value = true
}

// 处理邮件选择
const handleEmailSelect = () => {
  // 这里可以添加批量选择的逻辑
}

// 格式化时间
const formatTime = (time) => {
  const now = dayjs()
  const emailTime = dayjs(time)
  
  if (now.diff(emailTime, 'day') === 0) {
    return emailTime.format('HH:mm')
  } else if (now.diff(emailTime, 'day') < 7) {
    return emailTime.format('ddd HH:mm')
  } else {
    return emailTime.format('MM-DD')
  }
}

// 处理删除邮件
const handleDeleteEmail = (emailId) => {
  const index = emails.value.findIndex(e => e.id === emailId)
  if (index > -1) {
    emails.value.splice(index, 1)
    total.value--
  }
  showEmailDetail.value = false
  selectedEmail.value = null
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchEmails()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchEmails()
}

// 批量删除
const deleteSelected = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedEmails.value.length} 封邮件吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const emailIds = selectedEmails.value.map(email => email.id)
    await api.delete('/api/emails/batch', { data: { email_ids: emailIds } })
    
    // 从列表中移除已删除的邮件
    selectedEmails.value.forEach(email => {
      const index = emails.value.findIndex(e => e.id === email.id)
      if (index > -1) {
        emails.value.splice(index, 1)
      }
    })
    
    total.value -= emailIds.length
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  fetchEmails()
})
</script>

<style scoped>
.sent {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.toolbar-left h2 {
  margin: 0;
  color: #333;
}

.email-list-card {
  min-height: 600px;
}

.loading-state,
.empty-state {
  padding: 40px 0;
}

.email-list {
  max-height: 70vh;
  overflow-y: auto;
}

.email-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
  gap: 12px;
}

.email-item:hover {
  background-color: #f5f5f5;
}

.email-item.selected {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.email-checkbox {
  flex-shrink: 0;
  padding-top: 2px;
}

.email-content {
  flex: 1;
  min-width: 0;
}

.email-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.recipient {
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.email-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.attachment-icon {
  color: #666;
  font-size: 14px;
}

.time {
  color: #999;
  font-size: 12px;
  white-space: nowrap;
}

.email-subject {
  color: #666;
  margin-bottom: 5px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.email-preview {
  color: #999;
  font-size: 12px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.batch-toolbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 1000;
}

.batch-info {
  color: #666;
  font-size: 14px;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .sent {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .toolbar-right {
    width: 100%;
  }
  
  .toolbar-right .el-input {
    width: 100% !important;
  }
  
  .email-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .recipient {
    max-width: 100%;
  }
  
  .batch-toolbar {
    left: 10px;
    right: 10px;
    transform: none;
    flex-direction: column;
    gap: 10px;
  }
  
  .batch-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>
