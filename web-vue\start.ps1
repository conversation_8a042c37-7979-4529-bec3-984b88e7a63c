# Miko邮箱系统 Vue前端启动脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Miko邮箱系统 Vue前端启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Node.js
Write-Host "检查Node.js版本..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查npm
Write-Host ""
Write-Host "检查npm版本..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到npm" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查依赖
Write-Host ""
Write-Host "检查依赖是否已安装..." -ForegroundColor Yellow
if (!(Test-Path "node_modules")) {
    Write-Host "正在安装依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 依赖安装失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
    Write-Host "依赖安装完成" -ForegroundColor Green
} else {
    Write-Host "依赖已安装，跳过安装步骤" -ForegroundColor Green
}

# 启动开发服务器
Write-Host ""
Write-Host "启动开发服务器..." -ForegroundColor Yellow
Write-Host "请在浏览器中访问: http://localhost:3000" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Cyan
Write-Host ""

npm run dev
