<template>
  <div class="mailbox-management">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>邮箱管理</h2>
        <el-button @click="refreshMailboxes" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>

      <div class="toolbar-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建邮箱
        </el-button>
        <el-button @click="showBatchCreateDialog = true">
          <el-icon><DocumentAdd /></el-icon>
          批量创建
        </el-button>
      </div>
    </div>

    <!-- 邮箱列表 -->
    <el-card class="mailbox-list-card">
      <div v-if="loading && mailboxes.length === 0" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="mailboxes.length === 0" class="empty-state">
        <el-empty description="暂无邮箱" />
      </div>

      <div v-else>
        <el-table
          :data="mailboxes"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column prop="email" label="邮箱地址" min-width="200">
            <template #default="{ row }">
              <div class="email-cell">
                <span class="email-address">{{ row.email }}</span>
                <el-tag v-if="row.is_default" type="primary" size="small">默认</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="domain" label="域名" width="150" />

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === 'active' ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="email_count" label="邮件数量" width="100" />

          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="!row.is_default"
                size="small"
                @click="setAsDefault(row)"
              >
                设为默认
              </el-button>
              <el-button
                size="small"
                :type="row.status === 'active' ? 'warning' : 'success'"
                @click="toggleStatus(row)"
              >
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteMailbox(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div v-if="total > 0" class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 批量操作工具栏 -->
    <div v-if="selectedMailboxes.length > 0" class="batch-toolbar">
      <div class="batch-info">
        已选择 {{ selectedMailboxes.length }} 个邮箱
      </div>
      <div class="batch-actions">
        <el-button @click="batchEnable">批量启用</el-button>
        <el-button @click="batchDisable">批量禁用</el-button>
        <el-button type="danger" @click="batchDelete">批量删除</el-button>
      </div>
    </div>

    <!-- 创建邮箱对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建邮箱"
      width="500px"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="邮箱前缀" prop="prefix">
          <el-input
            v-model="createForm.prefix"
            placeholder="请输入邮箱前缀"
            clearable
          />
        </el-form-item>

        <el-form-item label="域名" prop="domain">
          <el-select
            v-model="createForm.domain"
            placeholder="选择域名"
            style="width: 100%"
          >
            <el-option
              v-for="domain in availableDomains"
              :key="domain"
              :label="domain"
              :value="domain"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="设为默认">
          <el-switch v-model="createForm.isDefault" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createMailbox" :loading="creating">
          创建
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量创建对话框 -->
    <el-dialog
      v-model="showBatchCreateDialog"
      title="批量创建邮箱"
      width="600px"
      @close="resetBatchCreateForm"
    >
      <el-form
        ref="batchCreateFormRef"
        :model="batchCreateForm"
        :rules="batchCreateRules"
        label-width="100px"
      >
        <el-form-item label="邮箱前缀" prop="prefixes">
          <el-input
            v-model="batchCreateForm.prefixes"
            type="textarea"
            :rows="6"
            placeholder="请输入邮箱前缀，每行一个&#10;例如：&#10;user1&#10;user2&#10;user3"
          />
          <div class="form-tip">
            每行输入一个邮箱前缀，最多支持 50 个
          </div>
        </el-form-item>

        <el-form-item label="域名" prop="domain">
          <el-select
            v-model="batchCreateForm.domain"
            placeholder="选择域名"
            style="width: 100%"
          >
            <el-option
              v-for="domain in availableDomains"
              :key="domain"
              :label="domain"
              :value="domain"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showBatchCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="batchCreateMailboxes" :loading="batchCreating">
          批量创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const batchCreating = ref(false)
const mailboxes = ref([])
const selectedMailboxes = ref([])
const availableDomains = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const showBatchCreateDialog = ref(false)

// 表单引用
const createFormRef = ref()
const batchCreateFormRef = ref()

// 创建表单
const createForm = reactive({
  prefix: '',
  domain: '',
  isDefault: false
})

// 批量创建表单
const batchCreateForm = reactive({
  prefixes: '',
  domain: ''
})

// 表单验证规则
const createRules = {
  prefix: [
    { required: true, message: '请输入邮箱前缀', trigger: 'blur' },
    { min: 1, max: 50, message: '前缀长度在 1 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9._-]+$/, message: '前缀只能包含字母、数字、点、下划线和连字符', trigger: 'blur' }
  ],
  domain: [
    { required: true, message: '请选择域名', trigger: 'change' }
  ]
}

const batchCreateRules = {
  prefixes: [
    { required: true, message: '请输入邮箱前缀', trigger: 'blur' },
    { validator: validatePrefixes, trigger: 'blur' }
  ],
  domain: [
    { required: true, message: '请选择域名', trigger: 'change' }
  ]
}

// 验证批量前缀
function validatePrefixes(rule, value, callback) {
  if (!value) {
    callback()
    return
  }

  const prefixes = value.split('\n').filter(p => p.trim())

  if (prefixes.length === 0) {
    callback(new Error('请至少输入一个邮箱前缀'))
    return
  }

  if (prefixes.length > 50) {
    callback(new Error('最多支持 50 个邮箱前缀'))
    return
  }

  const prefixRegex = /^[a-zA-Z0-9._-]+$/
  for (const prefix of prefixes) {
    if (!prefixRegex.test(prefix.trim())) {
      callback(new Error(`前缀格式不正确: ${prefix.trim()}`))
      return
    }
  }

  callback()
}

// 获取邮箱列表
const fetchMailboxes = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    const response = await api.get('/api/mailboxes', { params })
    if (response.data.code === 0) {
      mailboxes.value = response.data.data.mailboxes
      total.value = response.data.data.total
    }
  } catch (error) {
    console.error('获取邮箱列表失败:', error)
    ElMessage.error('获取邮箱列表失败')
  } finally {
    loading.value = false
  }
}

// 获取可用域名
const fetchAvailableDomains = async () => {
  try {
    const response = await api.get('/api/domains/available')
    if (response.data.code === 0) {
      availableDomains.value = response.data.data
    }
  } catch (error) {
    console.error('获取可用域名失败:', error)
  }
}

// 刷新邮箱列表
const refreshMailboxes = () => {
  fetchMailboxes()
}

// 格式化日期时间
const formatDateTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedMailboxes.value = selection
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchMailboxes()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchMailboxes()
}

// 创建邮箱
const createMailbox = async () => {
  if (!createFormRef.value) return

  try {
    const valid = await createFormRef.value.validate()
    if (!valid) return

    creating.value = true

    const data = {
      prefix: createForm.prefix,
      domain: createForm.domain,
      is_default: createForm.isDefault
    }

    const response = await api.post('/api/mailboxes', data)
    if (response.data.code === 0) {
      ElMessage.success('邮箱创建成功')
      showCreateDialog.value = false
      fetchMailboxes()
    } else {
      ElMessage.error(response.data.msg || response.data.message || '创建失败')
    }
  } catch (error) {
    console.error('创建邮箱失败:', error)
    ElMessage.error('创建失败')
  } finally {
    creating.value = false
  }
}

// 批量创建邮箱
const batchCreateMailboxes = async () => {
  if (!batchCreateFormRef.value) return

  try {
    const valid = await batchCreateFormRef.value.validate()
    if (!valid) return

    batchCreating.value = true

    const prefixes = batchCreateForm.prefixes
      .split('\n')
      .map(p => p.trim())
      .filter(p => p)

    const data = {
      prefixes,
      domain: batchCreateForm.domain
    }

    const response = await api.post('/api/mailboxes/batch', data)
    if (response.data.code === 0) {
      ElMessage.success(`成功创建 ${prefixes.length} 个邮箱`)
      showBatchCreateDialog.value = false
      fetchMailboxes()
    } else {
      ElMessage.error(response.data.msg || response.data.message || '批量创建失败')
    }
  } catch (error) {
    console.error('批量创建邮箱失败:', error)
    ElMessage.error('批量创建失败')
  } finally {
    batchCreating.value = false
  }
}

// 设为默认邮箱
const setAsDefault = async (mailbox) => {
  try {
    await api.put(`/api/mailboxes/${mailbox.id}/default`)
    ElMessage.success('设置成功')
    fetchMailboxes()
  } catch (error) {
    console.error('设置默认邮箱失败:', error)
    ElMessage.error('设置失败')
  }
}

// 切换状态
const toggleStatus = async (mailbox) => {
  try {
    const newStatus = mailbox.status === 'active' ? 'disabled' : 'active'
    await api.put(`/api/mailboxes/${mailbox.id}/status`, { status: newStatus })
    ElMessage.success('状态更新成功')
    fetchMailboxes()
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新失败')
  }
}

// 删除邮箱
const deleteMailbox = async (mailbox) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除邮箱 ${mailbox.email} 吗？删除后无法恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.delete(`/api/mailboxes/${mailbox.id}`)
    ElMessage.success('删除成功')
    fetchMailboxes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除邮箱失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量操作
const batchEnable = async () => {
  try {
    const ids = selectedMailboxes.value.map(m => m.id)
    await api.put('/api/mailboxes/batch/enable', { ids })
    ElMessage.success('批量启用成功')
    fetchMailboxes()
  } catch (error) {
    console.error('批量启用失败:', error)
    ElMessage.error('批量启用失败')
  }
}

const batchDisable = async () => {
  try {
    const ids = selectedMailboxes.value.map(m => m.id)
    await api.put('/api/mailboxes/batch/disable', { ids })
    ElMessage.success('批量禁用成功')
    fetchMailboxes()
  } catch (error) {
    console.error('批量禁用失败:', error)
    ElMessage.error('批量禁用失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedMailboxes.value.length} 个邮箱吗？删除后无法恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedMailboxes.value.map(m => m.id)
    await api.delete('/api/mailboxes/batch', { data: { ids } })
    ElMessage.success('批量删除成功')
    fetchMailboxes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 重置表单
const resetCreateForm = () => {
  createForm.prefix = ''
  createForm.domain = ''
  createForm.isDefault = false
  if (createFormRef.value) {
    createFormRef.value.resetFields()
  }
}

const resetBatchCreateForm = () => {
  batchCreateForm.prefixes = ''
  batchCreateForm.domain = ''
  if (batchCreateFormRef.value) {
    batchCreateFormRef.value.resetFields()
  }
}

onMounted(() => {
  fetchMailboxes()
  fetchAvailableDomains()
})
</script>

<style scoped>
.mailbox-management {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.toolbar-left h2 {
  margin: 0;
  color: #333;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

.mailbox-list-card {
  min-height: 600px;
}

.loading-state,
.empty-state {
  padding: 40px 0;
}

.email-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.email-address {
  font-weight: 500;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.batch-toolbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 1000;
}

.batch-info {
  color: #666;
  font-size: 14px;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .mailbox-management {
    padding: 10px;
  }

  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .toolbar-right {
    justify-content: center;
  }

  .batch-toolbar {
    left: 10px;
    right: 10px;
    transform: none;
    flex-direction: column;
    gap: 10px;
  }

  .batch-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>
