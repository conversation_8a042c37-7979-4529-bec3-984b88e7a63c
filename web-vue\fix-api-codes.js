// 批量修复API响应码的脚本
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 需要修复的文件列表
const filesToFix = [
  'src/views/email/Inbox.vue',
  'src/views/email/Sent.vue', 
  'src/views/email/Compose.vue',
  'src/views/mailbox/index.vue',
  'src/views/forward/index.vue',
  'src/views/settings/index.vue',
  'src/views/admin/Dashboard.vue',
  'src/views/admin/Users.vue',
  'src/views/admin/Domains.vue',
  'src/views/admin/Mailboxes.vue',
  'src/views/auth/Register.vue'
];

console.log('🔧 开始修复API响应码...\n');

let totalFixed = 0;

filesToFix.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }
  
  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    const originalContent = content;
    
    // 替换 response.data.code === 200 为 response.data.code === 0
    content = content.replace(/response\.data\.code === 200/g, 'response.data.code === 0');
    
    // 替换 response.data.message 为 response.data.msg || response.data.message
    content = content.replace(/response\.data\.message/g, 'response.data.msg || response.data.message');
    
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      totalFixed++;
    } else {
      console.log(`ℹ️  无需修复: ${filePath}`);
    }
    
  } catch (error) {
    console.log(`❌ 修复失败: ${filePath} - ${error.message}`);
  }
});

console.log(`\n🎉 修复完成！共修复了 ${totalFixed} 个文件`);
console.log('\n📋 修复内容:');
console.log('  - response.data.code === 200 → response.data.code === 0');
console.log('  - response.data.message → response.data.msg || response.data.message');
console.log('\n💡 现在前端API调用应该与后端响应格式匹配了！');
