{"name": "miko-email-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/vue-fontawesome": "^3.1.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^9.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.5.6", "prettier": "^3.1.1", "sass": "^1.69.7", "tailwindcss": "^4.1.11", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.8"}}