/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        'space-dark': '#0f172a',
        'space-blue': '#1e293b',
        'glass-border': 'rgba(148, 163, 184, 0.2)',
        'glass-bg': 'rgba(15, 23, 42, 0.8)',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
      },
      backdropBlur: {
        'glass': '16px',
      },
      animation: {
        'breathing': 'breathing 2s ease-in-out infinite',
        'float': 'float 6s ease-in-out infinite',
      },
      keyframes: {
        breathing: {
          '0%, 100%': { opacity: '0.6' },
          '50%': { opacity: '1' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
      },
      screens: {
        'xl': '1200px',
        'lg': '768px',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
