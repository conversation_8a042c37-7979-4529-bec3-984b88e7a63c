# Miko邮箱系统 - Vue前端

这是Miko邮箱系统的Vue 3前端项目，使用现代化的技术栈构建。

## 🚀 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 下一代前端构建工具
- **Element Plus** - Vue 3 UI组件库
- **Vue Router** - 官方路由管理器
- **Pinia** - Vue状态管理库
- **Axios** - HTTP客户端
- **Sass** - CSS预处理器

## 📦 安装依赖

```bash
# 进入前端目录
cd web-vue

# 安装依赖
npm install
```

## 🛠️ 开发

```bash
# 启动开发服务器
npm run dev
```

开发服务器将在 `http://localhost:3000` 启动，并自动代理API请求到后端服务器 `http://localhost:8080`。

## 🏗️ 构建

```bash
# 构建生产版本
npm run build
```

构建后的文件将输出到 `../web/dist` 目录，可以直接被Go后端服务器提供静态文件服务。

## 📁 项目结构

```
web-vue/
├── src/
│   ├── components/          # 公共组件
│   │   └── EmailDetail.vue  # 邮件详情组件
│   ├── layout/              # 布局组件
│   │   └── index.vue        # 主布局
│   ├── router/              # 路由配置
│   │   └── index.js         # 路由定义
│   ├── stores/              # 状态管理
│   │   └── auth.js          # 认证状态
│   ├── styles/              # 全局样式
│   │   └── index.scss       # 主样式文件
│   ├── utils/               # 工具函数
│   │   └── api.js           # API请求封装
│   ├── views/               # 页面组件
│   │   ├── admin/           # 管理员页面
│   │   ├── auth/            # 认证页面
│   │   │   ├── Login.vue    # 用户登录
│   │   │   ├── Register.vue # 用户注册
│   │   │   └── AdminLogin.vue # 管理员登录
│   │   ├── dashboard/       # 仪表板
│   │   │   └── index.vue    # 主仪表板
│   │   ├── email/           # 邮件相关
│   │   │   ├── Inbox.vue    # 收件箱
│   │   │   ├── Sent.vue     # 已发送
│   │   │   └── Compose.vue  # 写邮件
│   │   ├── mailbox/         # 邮箱管理
│   │   │   └── index.vue    # 邮箱列表
│   │   ├── forward/         # 邮件转发
│   │   └── settings/        # 设置页面
│   ├── App.vue              # 根组件
│   └── main.js              # 入口文件
├── index.html               # HTML模板
├── package.json             # 项目配置
├── vite.config.js           # Vite配置
└── README.md                # 项目说明
```

## 🎯 主要功能

### 用户功能
- ✅ 用户登录/注册
- ✅ 仪表板概览
- ✅ 收件箱管理
- ✅ 邮件发送
- ✅ 已发送邮件
- ✅ 邮箱管理（创建、删除、批量操作）
- ✅ 邮件转发设置
- ✅ 个人设置

### 管理员功能
- ✅ 管理员登录
- ✅ 用户管理
- ✅ 域名管理
- ✅ 邮箱分配

### 技术特性
- ✅ 响应式设计，支持移动端
- ✅ 路由守卫和权限控制
- ✅ 统一的API错误处理
- ✅ 组件懒加载
- ✅ 自动导入Element Plus组件
- ✅ Sass样式预处理
- ✅ 开发热重载

## 🔧 配置说明

### API代理配置

开发环境下，Vite会自动将 `/api` 开头的请求代理到后端服务器：

```javascript
// vite.config.js
server: {
  port: 3000,
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true
    }
  }
}
```

### 构建配置

生产构建会将文件输出到Go项目的web目录：

```javascript
// vite.config.js
build: {
  outDir: '../web/dist',
  emptyOutDir: true
}
```

## 🚦 开发指南

### 添加新页面

1. 在 `src/views/` 下创建新的Vue组件
2. 在 `src/router/index.js` 中添加路由配置
3. 如需要权限控制，在路由meta中添加相应配置

### 添加新的API接口

1. 在 `src/utils/api.js` 中添加请求方法
2. 或直接在组件中使用 `api.get/post/put/delete` 方法

### 状态管理

使用Pinia进行状态管理，已配置认证状态管理，可参考 `src/stores/auth.js`。

## 🔗 与后端集成

前端项目构建后的静态文件会输出到 `../web/dist` 目录，Go后端服务器会自动提供这些静态文件的服务。

确保后端服务器在 `http://localhost:8080` 运行，前端开发服务器会自动代理API请求。

## 📱 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
