// 认证状态调试脚本
// 在浏览器控制台中运行此脚本来检查认证状态

console.log('🔍 认证状态调试信息');
console.log('========================');

// 检查localStorage中的认证信息
const token = localStorage.getItem('token');
const userInfo = localStorage.getItem('userInfo');
const isAdmin = localStorage.getItem('isAdmin');

console.log('📦 LocalStorage 数据:');
console.log('  token:', token);
console.log('  userInfo:', userInfo);
console.log('  isAdmin:', isAdmin);

if (userInfo) {
  try {
    const parsedUserInfo = JSON.parse(userInfo);
    console.log('  解析后的用户信息:', parsedUserInfo);
  } catch (e) {
    console.log('  ❌ 用户信息解析失败:', e);
  }
}

// 检查当前路由
console.log('\n🛣️ 路由信息:');
console.log('  当前路径:', window.location.pathname);
console.log('  完整URL:', window.location.href);

// 检查Vue应用状态（如果可用）
if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
  console.log('\n🔧 Vue DevTools 可用');
} else {
  console.log('\n⚠️ Vue DevTools 不可用');
}

// 检查网络请求
console.log('\n🌐 建议检查:');
console.log('  1. 打开Network面板查看API请求');
console.log('  2. 查看Console面板的错误信息');
console.log('  3. 检查认证状态是否正确设置');

// 提供测试函数
window.testAuth = function() {
  console.log('🧪 测试认证状态...');
  
  // 模拟检查认证状态
  const hasToken = !!localStorage.getItem('token');
  const hasUserInfo = !!localStorage.getItem('userInfo');
  const isAdminUser = localStorage.getItem('isAdmin') === 'true';
  
  console.log('认证检查结果:');
  console.log('  有token:', hasToken);
  console.log('  有用户信息:', hasUserInfo);
  console.log('  是管理员:', isAdminUser);
  
  if (hasToken && hasUserInfo) {
    console.log('✅ 认证状态正常');
    if (isAdminUser) {
      console.log('✅ 管理员权限正常');
      console.log('💡 应该可以访问 /admin/dashboard');
    } else {
      console.log('ℹ️ 普通用户权限');
      console.log('💡 应该访问 /dashboard');
    }
  } else {
    console.log('❌ 认证状态异常');
    console.log('💡 应该跳转到登录页');
  }
};

console.log('\n💡 使用方法:');
console.log('  在控制台运行 testAuth() 来测试认证状态');
console.log('  检查Network面板查看API请求状态');
console.log('  查看Console面板的错误信息');
