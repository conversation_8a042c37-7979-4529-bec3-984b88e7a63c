import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/utils/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const isAdmin = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const username = computed(() => user.value?.username || '')

  // 设置认证信息
  const setAuth = (userData, authToken) => {
    user.value = userData
    token.value = authToken
    // 从用户数据中读取管理员状态
    isAdmin.value = userData?.is_admin === true
    localStorage.setItem('token', authToken)
    localStorage.setItem('user', JSON.stringify(userData))
    localStorage.setItem('isAdmin', isAdmin.value.toString())
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    token.value = ''
    isAdmin.value = false
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('isAdmin')
  }

  // 检查认证状态
  const checkAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    const savedIsAdmin = localStorage.getItem('isAdmin')

    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        isAdmin.value = savedIsAdmin === 'true'
      } catch (error) {
        console.error('解析用户信息失败:', error)
        clearAuth()
      }
    }
  }

  // 用户登录
  const login = async (username, password) => {
    try {
      const response = await api.post('/api/login', {
        username,
        password
      })

      if (response.data.code === 0) {
        const userData = response.data.data.user || response.data.data
        setAuth(userData, 'user-token') // 这里应该从后端返回真实token
        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(response.data.msg || response.data.message || '登录失败')
        return false
      }
    } catch (error) {
      console.error('登录错误:', error)
      ElMessage.error('登录失败，请检查网络连接')
      return false
    }
  }

  // 管理员登录
  const adminLogin = async (username, password) => {
    try {
      const response = await api.post('/api/admin/login', {
        username,
        password
      })

      if (response.data.code === 0) {
        const userData = response.data.data.user || response.data.data
        setAuth(userData, 'admin-token') // 这里应该从后端返回真实token
        ElMessage.success('管理员登录成功')
        return true
      } else {
        ElMessage.error(response.data.msg || response.data.message || '登录失败')
        return false
      }
    } catch (error) {
      console.error('管理员登录错误:', error)
      ElMessage.error('登录失败，请检查网络连接')
      return false
    }
  }

  // 用户注册
  const register = async (userData) => {
    try {
      const response = await api.post('/api/register', userData)

      if (response.data.code === 0) {
        ElMessage.success('注册成功，请登录')
        return true
      } else {
        ElMessage.error(response.data.msg || response.data.message || '注册失败')
        return false
      }
    } catch (error) {
      console.error('注册错误:', error)
      ElMessage.error('注册失败，请检查网络连接')
      return false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      await api.post('/api/logout')
    } catch (error) {
      console.error('退出登录错误:', error)
    } finally {
      clearAuth()
      ElMessage.success('已退出登录')
    }
  }

  return {
    // 状态
    user,
    token,
    isAdmin,
    // 计算属性
    isAuthenticated,
    username,
    // 方法
    setAuth,
    clearAuth,
    checkAuth,
    login,
    adminLogin,
    register,
    logout
  }
})
