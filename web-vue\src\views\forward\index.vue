<template>
  <div class="forward-management">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>邮件转发</h2>
        <el-button @click="refreshForwards" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加转发规则
        </el-button>
      </div>
    </div>

    <!-- 转发规则列表 -->
    <el-card class="forward-list-card">
      <div v-if="loading && forwards.length === 0" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="forwards.length === 0" class="empty-state">
        <el-empty description="暂无转发规则" />
      </div>

      <div v-else>
        <el-table
          :data="forwards"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="from_email" label="源邮箱" min-width="200" />
          
          <el-table-column prop="to_email" label="转发到" min-width="200" />
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="forward_count" label="转发次数" width="120" />
          
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button
                size="small"
                :type="row.status === 'active' ? 'warning' : 'success'"
                @click="toggleStatus(row)"
              >
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button
                size="small"
                @click="editForward(row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteForward(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div v-if="total > 0" class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 批量操作工具栏 -->
    <div v-if="selectedForwards.length > 0" class="batch-toolbar">
      <div class="batch-info">
        已选择 {{ selectedForwards.length }} 条转发规则
      </div>
      <div class="batch-actions">
        <el-button @click="batchEnable">批量启用</el-button>
        <el-button @click="batchDisable">批量禁用</el-button>
        <el-button type="danger" @click="batchDelete">批量删除</el-button>
      </div>
    </div>

    <!-- 创建/编辑转发规则对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingForward ? '编辑转发规则' : '创建转发规则'"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="源邮箱" prop="fromEmail">
          <el-select
            v-model="form.fromEmail"
            placeholder="选择源邮箱"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="mailbox in userMailboxes"
              :key="mailbox.id"
              :label="mailbox.email"
              :value="mailbox.email"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="转发到" prop="toEmail">
          <el-input
            v-model="form.toEmail"
            placeholder="请输入转发目标邮箱"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="form.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveForward" :loading="saving">
          {{ editingForward ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const forwards = ref([])
const selectedForwards = ref([])
const userMailboxes = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const editingForward = ref(null)

// 表单引用
const formRef = ref()

// 表单数据
const form = reactive({
  fromEmail: '',
  toEmail: '',
  isActive: true
})

// 表单验证规则
const formRules = {
  fromEmail: [
    { required: true, message: '请选择源邮箱', trigger: 'change' }
  ],
  toEmail: [
    { required: true, message: '请输入转发目标邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 获取转发规则列表
const fetchForwards = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }
    
    const response = await api.get('/api/forwards', { params })
    if (response.data.code === 200) {
      forwards.value = response.data.data.forwards
      total.value = response.data.data.total
    }
  } catch (error) {
    console.error('获取转发规则列表失败:', error)
    ElMessage.error('获取转发规则列表失败')
  } finally {
    loading.value = false
  }
}

// 获取用户邮箱列表
const fetchUserMailboxes = async () => {
  try {
    const response = await api.get('/api/mailboxes')
    if (response.data.code === 200) {
      userMailboxes.value = response.data.data
    }
  } catch (error) {
    console.error('获取邮箱列表失败:', error)
  }
}

// 刷新转发规则列表
const refreshForwards = () => {
  fetchForwards()
}

// 格式化日期时间
const formatDateTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedForwards.value = selection
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchForwards()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchForwards()
}

// 编辑转发规则
const editForward = (forward) => {
  editingForward.value = forward
  form.fromEmail = forward.from_email
  form.toEmail = forward.to_email
  form.isActive = forward.status === 'active'
  showCreateDialog.value = true
}

// 保存转发规则
const saveForward = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    saving.value = true
    
    const data = {
      from_email: form.fromEmail,
      to_email: form.toEmail,
      status: form.isActive ? 'active' : 'disabled'
    }
    
    let response
    if (editingForward.value) {
      response = await api.put(`/api/forwards/${editingForward.value.id}`, data)
    } else {
      response = await api.post('/api/forwards', data)
    }
    
    if (response.data.code === 200) {
      ElMessage.success(editingForward.value ? '更新成功' : '创建成功')
      showCreateDialog.value = false
      fetchForwards()
    } else {
      ElMessage.error(response.data.message || '操作失败')
    }
  } catch (error) {
    console.error('保存转发规则失败:', error)
    ElMessage.error('操作失败')
  } finally {
    saving.value = false
  }
}

// 切换状态
const toggleStatus = async (forward) => {
  try {
    const newStatus = forward.status === 'active' ? 'disabled' : 'active'
    await api.put(`/api/forwards/${forward.id}/status`, { status: newStatus })
    ElMessage.success('状态更新成功')
    fetchForwards()
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新失败')
  }
}

// 删除转发规则
const deleteForward = async (forward) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除从 ${forward.from_email} 到 ${forward.to_email} 的转发规则吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.delete(`/api/forwards/${forward.id}`)
    ElMessage.success('删除成功')
    fetchForwards()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除转发规则失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量操作
const batchEnable = async () => {
  try {
    const ids = selectedForwards.value.map(f => f.id)
    await api.put('/api/forwards/batch/enable', { ids })
    ElMessage.success('批量启用成功')
    fetchForwards()
  } catch (error) {
    console.error('批量启用失败:', error)
    ElMessage.error('批量启用失败')
  }
}

const batchDisable = async () => {
  try {
    const ids = selectedForwards.value.map(f => f.id)
    await api.put('/api/forwards/batch/disable', { ids })
    ElMessage.success('批量禁用成功')
    fetchForwards()
  } catch (error) {
    console.error('批量禁用失败:', error)
    ElMessage.error('批量禁用失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedForwards.value.length} 条转发规则吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedForwards.value.map(f => f.id)
    await api.delete('/api/forwards/batch', { data: { ids } })
    ElMessage.success('批量删除成功')
    fetchForwards()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 重置表单
const resetForm = () => {
  editingForward.value = null
  form.fromEmail = ''
  form.toEmail = ''
  form.isActive = true
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  fetchForwards()
  fetchUserMailboxes()
})
</script>

<style scoped>
.forward-management {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.toolbar-left h2 {
  margin: 0;
  color: #333;
}

.forward-list-card {
  min-height: 600px;
}

.loading-state,
.empty-state {
  padding: 40px 0;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.batch-toolbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 1000;
}

.batch-info {
  color: #666;
  font-size: 14px;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .forward-management {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .batch-toolbar {
    left: 10px;
    right: 10px;
    transform: none;
    flex-direction: column;
    gap: 10px;
  }
  
  .batch-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>
