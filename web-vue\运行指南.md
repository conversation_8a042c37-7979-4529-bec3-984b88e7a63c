# Vue前端项目运行指南

## 🚨 PowerShell执行策略问题解决

如果遇到PowerShell执行策略错误，请使用以下任一方法：

### 方法1: 使用CMD命令行
```cmd
# 打开CMD命令行
cd d:\miko-email-1\web-vue
npm install
npm run dev
```

### 方法2: 临时修改PowerShell执行策略
```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
cd d:\miko-email-1\web-vue
npm install
npm run dev
```

### 方法3: 使用VS Code终端
1. 在VS Code中打开项目
2. 打开终端 (Ctrl + `)
3. 运行命令：
```bash
cd web-vue
npm install
npm run dev
```

## 📋 运行步骤

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问应用
打开浏览器访问: `http://localhost:3000`

## 🔧 可能遇到的问题

### 问题1: 端口被占用
如果3000端口被占用，Vite会自动使用下一个可用端口（如3001）

### 问题2: 后端API连接
确保Go后端服务器在 `http://localhost:8080` 运行

### 问题3: 依赖安装失败
尝试清除npm缓存：
```bash
npm cache clean --force
npm install
```

## 🎯 测试功能

### 基本功能测试
1. **登录页面**: 访问 `http://localhost:3000/login`
2. **注册页面**: 访问 `http://localhost:3000/register`
3. **管理员登录**: 访问 `http://localhost:3000/admin/login`

### 路由测试
- 未登录状态应自动跳转到登录页
- 登录后应跳转到仪表板
- 管理员登录后应跳转到管理面板

### 响应式测试
- 调整浏览器窗口大小测试移动端适配
- 使用浏览器开发者工具模拟移动设备

## 🏗️ 构建生产版本

```bash
npm run build
```

构建后的文件会输出到 `../web/dist` 目录，Go后端会自动提供静态文件服务。

## 📱 功能清单

### ✅ 已实现功能
- [x] 用户认证系统
- [x] 响应式仪表板
- [x] 邮件管理（收件箱/发件箱）
- [x] 邮件发送（支持附件）
- [x] 邮箱管理
- [x] 邮件转发设置
- [x] 个人设置
- [x] 管理员功能

### 🎨 UI特性
- [x] 现代化设计
- [x] 移动端适配
- [x] 暗色主题支持
- [x] 动画过渡效果
- [x] 加载状态提示

## 🔍 调试技巧

### 开发者工具
- 使用Vue DevTools浏览器扩展
- 检查Network面板查看API请求
- 使用Console查看错误信息

### 常用调试命令
```bash
# 检查语法错误
npm run lint

# 查看详细错误信息
npm run dev --debug
```

## 📞 技术支持

如果遇到问题，请检查：
1. Node.js版本 >= 16
2. npm版本 >= 8
3. 网络连接正常
4. 后端服务器运行状态

## 🎉 成功运行标志

当看到以下信息时，表示项目运行成功：
```
  VITE v5.0.8  ready in 1234 ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: use --host to expose
  ➜  press h to show help
```
