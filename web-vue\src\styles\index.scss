// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 布局样式
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  flex-shrink: 0;
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  flex-shrink: 0;
  width: 250px;
  background: #001529;
  overflow-y: auto;
}

.layout-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f5f5f5;
}

// 卡片样式
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

// 工具类
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.p-20 {
  padding: 20px;
}

// 响应式
@media (max-width: 768px) {
  .layout-sidebar {
    width: 200px;
  }
  
  .layout-content {
    padding: 10px;
  }
}

// Element Plus 自定义样式
.el-menu {
  border-right: none;
}

.el-menu--dark {
  background-color: #001529;
}

.el-menu--dark .el-menu-item {
  color: rgba(255, 255, 255, 0.65);
}

.el-menu--dark .el-menu-item:hover {
  background-color: #1890ff;
  color: white;
}

.el-menu--dark .el-menu-item.is-active {
  background-color: #1890ff;
  color: white;
}

// 邮件列表样式
.email-list {
  .email-item {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.unread {
      background-color: #f0f9ff;
      font-weight: 600;
    }
    
    .email-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .sender {
        font-weight: 600;
        color: #333;
      }
      
      .time {
        color: #999;
        font-size: 12px;
      }
    }
    
    .email-subject {
      color: #666;
      margin-bottom: 5px;
      font-size: 14px;
    }
    
    .email-preview {
      color: #999;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

// 统计卡片样式
.stats-card {
  text-align: center;
  padding: 20px;
  
  .stats-number {
    font-size: 32px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 8px;
  }
  
  .stats-label {
    color: #666;
    font-size: 14px;
  }
}
