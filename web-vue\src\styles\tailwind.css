@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    @apply dark;
  }

  body {
    @apply font-sans text-white min-h-screen;
    background: linear-gradient(to bottom right, #0f172a, #1e293b);
  }
}

@layer components {
  /* 玻璃拟态卡片样式 */
  .glass-card {
    @apply border border-slate-700 rounded-xl shadow-2xl;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(16px);
  }

  /* 文字渐变效果 */
  .text-gradient {
    background: linear-gradient(to right, #22d3ee, #3b82f6);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  /* 激活态导航项 */
  .nav-active {
    @apply relative;
  }

  .nav-active::before {
    content: '';
    @apply absolute left-0 top-0 bottom-0 w-1 rounded-r-full;
    background: linear-gradient(to bottom, #22d3ee, #3b82f6);
    animation: breathing 2s ease-in-out infinite;
  }

  /* 毛玻璃导航栏 */
  .glass-nav {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(16px);
    @apply border-b border-slate-700;
  }

  /* 空状态样式 */
  .empty-state {
    @apply border-2 border-dashed border-slate-600 rounded-lg p-8 text-center text-slate-400;
  }

  /* 按钮样式 */
  .btn-primary {
    @apply text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
    background: linear-gradient(to right, #06b6d4, #3b82f6);
  }

  .btn-primary:hover {
    background: linear-gradient(to right, #0891b2, #2563eb);
  }

  .btn-secondary {
    @apply bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 border border-slate-600;
  }

  /* 输入框样式 */
  .input-glass {
    @apply border border-slate-600 rounded-lg px-3 py-2 text-white transition-all duration-200;
    background: rgba(30, 41, 59, 0.5);
  }

  .input-glass::placeholder {
    @apply text-slate-400;
  }

  .input-glass:focus {
    @apply border-cyan-400 outline-none;
    box-shadow: 0 0 0 1px #22d3ee;
  }
}

/* 动画关键帧 */
@keyframes breathing {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@layer utilities {
  /* 装饰性渐变圆形 */
  .decoration-circle-cyan {
    @apply fixed w-96 h-96 rounded-full pointer-events-none;
    top: -12rem;
    right: -12rem;
    background: radial-gradient(circle, rgba(34, 211, 238, 0.2) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }

  .decoration-circle-coral {
    @apply fixed w-80 h-80 rounded-full pointer-events-none;
    bottom: -10rem;
    left: -10rem;
    background: radial-gradient(circle, rgba(248, 113, 113, 0.2) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }

  /* 动画工具类 */
  .animate-breathing {
    animation: breathing 2s ease-in-out infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
}
