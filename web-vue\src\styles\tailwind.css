@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply box-border;
  }

  html {
    @apply dark;
  }

  body {
    @apply font-sans text-white bg-gradient-to-br from-space-dark to-space-blue min-h-screen;
  }
}

@layer components {
  /* 玻璃拟态卡片样式 */
  .glass-card {
    @apply backdrop-blur-md bg-glass-bg bg-opacity-80 border border-slate-700 rounded-xl shadow-2xl;
  }

  /* 文字渐变效果 */
  .text-gradient {
    @apply bg-gradient-to-r from-cyan-400 to-blue-400 text-transparent bg-clip-text;
  }

  /* 激活态导航项 */
  .nav-active {
    @apply relative;
  }

  .nav-active::before {
    @apply content-[''] absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-cyan-400 to-blue-400 rounded-r-full animate-breathing;
  }

  /* 毛玻璃导航栏 */
  .glass-nav {
    @apply backdrop-blur-glass bg-slate-900/80 border-b border-slate-700/50;
  }

  /* 空状态样式 */
  .empty-state {
    @apply border-2 border-dashed border-slate-600 rounded-lg p-8 text-center text-slate-400;
  }

  /* 按钮样式 */
  .btn-primary {
    @apply bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 border border-slate-600;
  }

  /* 输入框样式 */
  .input-glass {
    @apply bg-slate-800/50 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400 transition-all duration-200;
  }
}

@layer utilities {
  /* 装饰性渐变圆形 */
  .decoration-circle-cyan {
    @apply fixed w-96 h-96 rounded-full pointer-events-none;
    top: -12rem;
    right: -12rem;
    background: radial-gradient(circle, rgba(34, 211, 238, 0.2) 0%, transparent 70%);
  }

  .decoration-circle-coral {
    @apply fixed w-80 h-80 rounded-full pointer-events-none;
    bottom: -10rem;
    left: -10rem;
    background: radial-gradient(circle, rgba(248, 113, 113, 0.2) 0%, transparent 70%);
  }
}
