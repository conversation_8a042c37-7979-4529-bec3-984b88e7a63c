/* 深色科技风主题样式 */

/* 基础样式 */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: white;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* 玻璃拟态卡片 */
.glass-card {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 文字渐变 */
.text-gradient {
  background: linear-gradient(to right, #22d3ee, #3b82f6);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: bold;
}

/* 毛玻璃导航栏 */
.glass-nav {
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(to right, #06b6d4, #3b82f6);
  color: white;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 4px 14px 0 rgba(6, 182, 212, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(to right, #0891b2, #2563eb);
  box-shadow: 0 6px 20px 0 rgba(6, 182, 212, 0.4);
  transform: translateY(-1px);
}

.btn-secondary {
  background: #374151;
  color: white;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #4b5563;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background: #4b5563;
}

/* 输入框样式 */
.input-glass {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid #4b5563;
  border-radius: 8px;
  padding: 12px;
  color: white;
  transition: all 0.2s;
  width: 100%;
}

.input-glass::placeholder {
  color: #94a3b8;
}

.input-glass:focus {
  outline: none;
  border-color: #22d3ee;
  box-shadow: 0 0 0 1px #22d3ee;
}

/* 激活态导航项 */
.nav-active {
  position: relative;
  background: linear-gradient(to right, rgba(6, 182, 212, 0.2), rgba(59, 130, 246, 0.2));
  color: #22d3ee;
}

.nav-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #22d3ee, #3b82f6);
  border-radius: 0 2px 2px 0;
  animation: breathing 2s ease-in-out infinite;
}

/* 空状态样式 */
.empty-state {
  border: 2px dashed #4b5563;
  border-radius: 8px;
  padding: 32px;
  text-align: center;
  color: #94a3b8;
}

/* 装饰性渐变圆形 */
.decoration-circle-cyan {
  position: fixed;
  width: 384px;
  height: 384px;
  border-radius: 50%;
  pointer-events: none;
  top: -192px;
  right: -192px;
  background: radial-gradient(circle, rgba(34, 211, 238, 0.15) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
  z-index: -1;
}

.decoration-circle-coral {
  position: fixed;
  width: 320px;
  height: 320px;
  border-radius: 50%;
  pointer-events: none;
  bottom: -160px;
  left: -160px;
  background: radial-gradient(circle, rgba(248, 113, 113, 0.15) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite 3s;
  z-index: -1;
}

/* 动画 */
@keyframes breathing {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.animate-breathing {
  animation: breathing 2s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 响应式工具类 */
.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }

.m-4 { margin: 1rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }

.space-x-4 > * + * { margin-left: 1rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-white { color: white; }
.text-slate-300 { color: #cbd5e1; }
.text-slate-400 { color: #94a3b8; }
.text-slate-500 { color: #64748b; }

.bg-slate-700 { background-color: #334155; }
.bg-slate-800 { background-color: #1e293b; }

.border { border-width: 1px; }
.border-slate-700 { border-color: #334155; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

.transition-all { transition: all 0.15s; }
.duration-200 { transition-duration: 200ms; }

.hover\:bg-slate-600:hover { background-color: #475569; }
.hover\:text-white:hover { color: white; }

.fixed { position: fixed; }
.relative { position: relative; }
.absolute { position: absolute; }

.top-0 { top: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.z-50 { z-index: 50; }

/* 响应式断点 */
@media (min-width: 768px) {
  .lg\:flex { display: flex; }
  .lg\:hidden { display: none; }
  .lg\:static { position: static; }
  .lg\:translate-x-0 { transform: translateX(0); }
}

@media (min-width: 1024px) {
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}
