/* 深空灰主题样式 - 参考Nebula Admin */

/* CSS变量定义 */
:root {
  --bg-dark: #1e1f26;
  --bg-darker: #16171d;
  --primary: #00B4D8;
  --primary-light: #48cae4;
  --accent: #FF6B6B;
  --text-primary: #f0f0f0;
  --text-secondary: #a0a0a0;
  --card-bg: #2c2f38;
  --border: #3a3d48;
  --success: #52b788;
  --warning: #ff9e00;
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.25);
}

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

html {
  height: 100%;
}

body {
  background-color: var(--bg-dark);
  color: var(--text-primary);
  min-height: 100vh;
  overflow-x: hidden;
}

/* 卡片样式 */
.glass-card, .card {
  background: var(--card-bg);
  border-radius: 10px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-action {
  color: var(--primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
}

.card-action:hover {
  color: var(--primary-light);
}

/* 文字渐变 */
.text-gradient {
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* 毛玻璃导航栏 */
.glass-nav {
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  color: white;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px 0 rgba(0, 180, 216, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(to right, #0891b2, #2563eb);
  box-shadow: 0 6px 20px 0 rgba(0, 180, 216, 0.4);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--card-bg);
  color: var(--text-primary);
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 8px;
  border: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--border);
  border-color: var(--primary);
}

/* 输入框样式 */
.input-glass {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid #4b5563;
  border-radius: 8px;
  padding: 12px;
  color: white;
  transition: all 0.2s;
  width: 100%;
}

.input-glass::placeholder {
  color: #94a3b8;
}

.input-glass:focus {
  outline: none;
  border-color: #22d3ee;
  box-shadow: 0 0 0 1px #22d3ee;
}

/* 激活态导航项 */
.nav-active {
  position: relative;
  background: linear-gradient(to right, rgba(6, 182, 212, 0.2), rgba(59, 130, 246, 0.2));
  color: #22d3ee;
}

.nav-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #22d3ee, #3b82f6);
  border-radius: 0 2px 2px 0;
  animation: breathing 2s ease-in-out infinite;
}

/* 空状态样式 */
.empty-state {
  border: 2px dashed #4b5563;
  border-radius: 8px;
  padding: 32px;
  text-align: center;
  color: #94a3b8;
}

/* 装饰性渐变圆形 */
.decoration-circle-cyan {
  position: fixed;
  width: 384px;
  height: 384px;
  border-radius: 50%;
  pointer-events: none;
  top: -192px;
  right: -192px;
  background: radial-gradient(circle, rgba(34, 211, 238, 0.15) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
  z-index: -1;
}

.decoration-circle-coral {
  position: fixed;
  width: 320px;
  height: 320px;
  border-radius: 50%;
  pointer-events: none;
  bottom: -160px;
  left: -160px;
  background: radial-gradient(circle, rgba(248, 113, 113, 0.15) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite 3s;
  z-index: -1;
}

/* 动画 */
@keyframes breathing {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.animate-breathing {
  animation: breathing 2s ease-in-out infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 表格样式 */
.table-responsive {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th {
  text-align: left;
  padding: 12px 15px;
  font-weight: 500;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border);
  font-size: 14px;
}

td {
  padding: 15px;
  border-bottom: 1px solid var(--border);
  color: var(--text-primary);
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background-color: rgba(0, 180, 216, 0.05);
}

/* 状态标签 */
.status {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  display: inline-block;
}

.status-active {
  background: rgba(82, 183, 136, 0.15);
  color: var(--success);
}

.status-pending {
  background: rgba(255, 158, 0, 0.15);
  color: var(--warning);
}

.status-warning {
  background: rgba(255, 107, 107, 0.15);
  color: var(--accent);
}

/* 加载状态 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(0, 180, 216, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.flex-1 { flex: 1 1 0%; }
.flex-grow { flex-grow: 1; }
.flex-shrink-0 { flex-shrink: 0; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }

.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-4 { margin-right: 1rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.leading-tight { line-height: 1.25; }
.leading-normal { line-height: 1.5; }

.border { border-width: 1px; }
.border-0 { border-width: 0; }
.border-t { border-top-width: 1px; }
.border-b { border-bottom-width: 1px; }

.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }

.overflow-hidden { overflow: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式断点 */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .xl\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
}
