<template>
  <div class="min-h-screen bg-gradient-to-br from-space-dark to-space-blue relative overflow-hidden">
    <!-- 装饰性渐变圆形 -->
    <div class="decoration-circle-cyan animate-float"></div>
    <div class="decoration-circle-coral animate-float" style="animation-delay: 3s;"></div>
    
    <!-- 固定顶部导航栏 -->
    <header class="fixed top-0 left-0 right-0 z-50 glass-nav">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex items-center space-x-4">
            <button 
              @click="toggleSidebar"
              class="lg:hidden p-2 rounded-lg text-slate-400 hover:text-white hover:bg-slate-700 transition-colors"
            >
              <font-awesome-icon :icon="isSidebarOpen ? 'times' : 'bars'" class="w-5 h-5" />
            </button>
            <h1 class="text-xl font-bold text-gradient">Miko邮箱</h1>
          </div>
          
          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <el-dropdown @command="handleCommand" trigger="click">
              <div class="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-slate-700/50 cursor-pointer transition-colors">
                <div class="w-8 h-8 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full flex items-center justify-center">
                  <font-awesome-icon icon="user" class="w-4 h-4 text-white" />
                </div>
                <span class="text-sm font-medium text-white">{{ authStore.user?.username || '用户' }}</span>
                <font-awesome-icon icon="chevron-down" class="w-3 h-3 text-slate-400" />
              </div>
              <template #dropdown>
                <el-dropdown-menu class="!bg-slate-800 !border-slate-700">
                  <el-dropdown-item command="profile" class="!text-white hover:!bg-slate-700">
                    <font-awesome-icon icon="user" class="w-4 h-4 mr-2" />
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="settings" class="!text-white hover:!bg-slate-700">
                    <font-awesome-icon icon="cog" class="w-4 h-4 mr-2" />
                    设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout" class="!text-red-400 hover:!bg-slate-700">
                    <font-awesome-icon icon="sign-out-alt" class="w-4 h-4 mr-2" />
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="pt-16 flex min-h-screen">
      <!-- 侧边栏 -->
      <aside 
        :class="[
          'fixed lg:static inset-y-0 left-0 z-40 w-64 transform transition-transform duration-300 ease-in-out lg:translate-x-0 pt-16 lg:pt-0',
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        ]"
      >
        <div class="h-full glass-card border-l-0 rounded-l-none">
          <nav class="p-4 space-y-2">
            <router-link
              v-for="item in menuItems"
              :key="item.path"
              :to="item.path"
              :class="[
                'flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group',
                $route.path === item.path 
                  ? 'nav-active bg-gradient-to-r from-cyan-500/20 to-blue-500/20 text-cyan-400' 
                  : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
              ]"
              @click="closeSidebarOnMobile"
            >
              <font-awesome-icon :icon="item.icon" class="w-5 h-5" />
              <span class="font-medium">{{ item.title }}</span>
            </router-link>
          </nav>
        </div>
      </aside>

      <!-- 遮罩层 (移动端) -->
      <div 
        v-if="isSidebarOpen" 
        @click="closeSidebar"
        class="fixed inset-0 z-30 bg-black/50 lg:hidden"
      ></div>

      <!-- 内容区域 -->
      <main class="flex-1 lg:ml-0 p-4 lg:p-6">
        <div class="max-w-7xl mx-auto">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式状态
const isSidebarOpen = ref(false)

// 菜单项配置
const menuItems = computed(() => {
  if (authStore.isAdmin) {
    return [
      { path: '/admin/dashboard', icon: 'chart-bar', title: '管理面板' },
      { path: '/admin/users', icon: 'users', title: '用户管理' },
      { path: '/admin/domains', icon: 'globe', title: '域名管理' },
      { path: '/admin/mailboxes', icon: 'envelope', title: '邮箱管理' },
    ]
  } else {
    return [
      { path: '/dashboard', icon: 'home', title: '仪表板' },
      { path: '/email/inbox', icon: 'inbox', title: '收件箱' },
      { path: '/email/sent', icon: 'paper-plane', title: '已发送' },
      { path: '/email/compose', icon: 'edit', title: '写邮件' },
      { path: '/mailbox', icon: 'envelope', title: '邮箱管理' },
      { path: '/forward', icon: 'share', title: '邮件转发' },
      { path: '/settings', icon: 'cog', title: '设置' },
    ]
  }
})

// 方法
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value
}

const closeSidebar = () => {
  isSidebarOpen.value = false
}

const closeSidebarOnMobile = () => {
  if (window.innerWidth < 1024) {
    closeSidebar()
  }
}

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await authStore.logout()
        ElMessage.success('退出登录成功')
        router.push('/login')
      } catch (error) {
        ElMessage.error('退出登录失败')
      }
      break
  }
}

// 响应式处理
const handleResize = () => {
  if (window.innerWidth >= 1024) {
    isSidebarOpen.value = false
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 自定义渐变背景 */
.decoration-circle-cyan {
  background: radial-gradient(circle, rgba(34, 211, 238, 0.2) 0%, transparent 70%);
}

.decoration-circle-coral {
  background: radial-gradient(circle, rgba(248, 113, 113, 0.2) 0%, transparent 70%);
}
</style>
