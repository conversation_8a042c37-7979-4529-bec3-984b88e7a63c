<template>
  <div class="app-container">
    <!-- 左侧导航栏 -->
    <div :class="['sidebar', { 'sidebar-collapsed': isSidebarCollapsed }]">
      <!-- Logo区域 -->
      <div class="logo">
        <font-awesome-icon icon="envelope" class="logo-icon" />
        <span class="logo-text">Miko邮箱</span>
      </div>

      <!-- 导航菜单 -->
      <div class="nav-menu">
        <div
          v-for="item in menuItems"
          :key="item.path"
          :class="['nav-item', { active: $route.path === item.path }]"
          @click="navigateTo(item.path)"
        >
          <font-awesome-icon :icon="item.icon" class="nav-icon" />
          <span class="nav-text">{{ item.title }}</span>
          <span v-if="item.badge" class="nav-badge">{{ item.badge }}</span>
        </div>
      </div>

      <!-- 用户面板 -->
      <div class="user-panel">
        <div class="user-avatar">{{ getUserInitials() }}</div>
        <div class="user-info">
          <div class="user-name">{{ authStore.user?.username || '用户' }}</div>
          <div class="user-role">{{ authStore.isAdmin ? '管理员' : '用户' }}</div>
        </div>
        <font-awesome-icon icon="chevron-down" class="expand-icon" />
      </div>
    </div>

    <!-- 右侧主内容区 -->
    <div class="main-content">
      <!-- 顶部栏 -->
      <div class="top-bar">
        <div class="page-title">{{ getPageTitle() }}</div>

        <div class="controls">
          <!-- 搜索框 -->
          <div class="search-box">
            <font-awesome-icon icon="search" class="search-icon" />
            <input type="text" placeholder="搜索..." v-model="searchQuery" />
          </div>

          <!-- 通知按钮 -->
          <button class="notification-btn" @click="showNotifications">
            <font-awesome-icon icon="bell" />
            <span v-if="notificationCount > 0" class="notification-badge">{{ notificationCount }}</span>
          </button>

          <!-- 用户菜单 -->
          <el-dropdown @command="handleCommand" trigger="click">
            <button class="user-btn">
              <div class="user-avatar-small">{{ getUserInitials() }}</div>
              <font-awesome-icon icon="chevron-down" />
            </button>
            <template #dropdown>
              <el-dropdown-menu class="user-dropdown-menu">
                <el-dropdown-item command="profile">
                  <font-awesome-icon icon="user" class="dropdown-icon" />
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <font-awesome-icon icon="cog" class="dropdown-icon" />
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <font-awesome-icon icon="sign-out-alt" class="dropdown-icon" />
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式状态
const isSidebarCollapsed = ref(false)
const searchQuery = ref('')
const notificationCount = ref(3)

// 菜单项配置
const menuItems = computed(() => {
  if (authStore.isAdmin) {
    return [
      { path: '/admin/dashboard', icon: 'chart-bar', title: '仪表盘' },
      { path: '/admin/users', icon: 'users', title: '用户管理' },
      { path: '/admin/domains', icon: 'globe', title: '域名管理' },
      { path: '/admin/mailboxes', icon: 'envelope', title: '邮箱管理' },
      { path: '/admin/settings', icon: 'cog', title: '系统设置' },
    ]
  } else {
    return [
      { path: '/dashboard', icon: 'chart-bar', title: '仪表盘' },
      { path: '/email/inbox', icon: 'inbox', title: '收件箱', badge: 5 },
      { path: '/email/sent', icon: 'paper-plane', title: '已发送' },
      { path: '/email/compose', icon: 'edit', title: '写邮件' },
      { path: '/mailbox', icon: 'envelope', title: '邮箱管理' },
      { path: '/forward', icon: 'share', title: '邮件转发' },
      { path: '/settings', icon: 'cog', title: '设置' },
    ]
  }
})

// 页面标题
const getPageTitle = () => {
  const currentItem = menuItems.value.find(item => item.path === route.path)
  return currentItem ? currentItem.title : '仪表盘'
}

// 用户头像首字母
const getUserInitials = () => {
  const username = authStore.user?.username || 'U'
  return username.charAt(0).toUpperCase()
}

// 方法
const navigateTo = (path) => {
  router.push(path)
}

const showNotifications = () => {
  ElMessage.info('通知功能开发中...')
}

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await authStore.logout()
        ElMessage.success('退出登录成功')
        router.push('/login')
      } catch (error) {
        ElMessage.error('退出登录失败')
      }
      break
  }
}

// 响应式处理
const handleResize = () => {
  if (window.innerWidth <= 768) {
    isSidebarCollapsed.value = true
  } else {
    isSidebarCollapsed.value = false
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  handleResize() // 初始化检查
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 应用容器 */
.app-container {
  display: flex;
  height: 100vh;
  background-color: var(--bg-dark);
}

/* 左侧导航栏 */
.sidebar {
  width: 260px;
  background-color: var(--bg-darker);
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border);
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.3);
  z-index: 10;
  transition: width 0.3s ease;
}

.sidebar-collapsed {
  width: 80px;
}

/* Logo区域 */
.logo {
  padding: 25px 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border);
}

.sidebar-collapsed .logo {
  justify-content: center;
  padding: 25px 10px;
}

.logo-icon {
  font-size: 28px;
  color: var(--primary);
  margin-right: 12px;
}

.sidebar-collapsed .logo-icon {
  margin-right: 0;
}

.logo-text {
  font-size: 22px;
  font-weight: 700;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sidebar-collapsed .logo-text {
  display: none;
}

/* 导航菜单 */
.nav-menu {
  padding: 20px 0;
  flex-grow: 1;
}

.nav-item {
  padding: 14px 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.sidebar-collapsed .nav-item {
  justify-content: center;
  padding: 18px 0;
}

.nav-item:hover {
  background: rgba(0, 180, 216, 0.1);
}

.nav-item.active {
  background: rgba(0, 180, 216, 0.15);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--primary);
}

.nav-icon {
  font-size: 22px;
  margin-right: 15px;
  color: var(--text-secondary);
}

.sidebar-collapsed .nav-icon {
  margin-right: 0;
  font-size: 24px;
}

.nav-item.active .nav-icon,
.nav-item:hover .nav-icon {
  color: var(--primary);
}

.nav-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.sidebar-collapsed .nav-text {
  display: none;
}

.nav-badge {
  margin-left: auto;
  background: var(--accent);
  color: white;
  border-radius: 12px;
  padding: 2px 10px;
  font-size: 12px;
  font-weight: 500;
}

.sidebar-collapsed .nav-badge {
  display: none;
}

/* 用户面板 */
.user-panel {
  padding: 20px;
  border-top: 1px solid var(--border);
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  color: white;
}

.user-info {
  flex-grow: 1;
}

.sidebar-collapsed .user-info {
  display: none;
}

.user-name {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.user-role {
  font-size: 13px;
  color: var(--text-secondary);
}

.expand-icon {
  color: var(--text-secondary);
}

.sidebar-collapsed .expand-icon {
  display: none;
}

/* 主内容区 */
.main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部栏 */
.top-bar {
  padding: 0 30px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border);
  background: rgba(30, 31, 38, 0.8);
  backdrop-filter: blur(5px);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.controls {
  display: flex;
  align-items: center;
}

/* 搜索框 */
.search-box {
  position: relative;
  margin-right: 20px;
}

.search-box input {
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 20px;
  padding: 10px 20px 10px 40px;
  color: var(--text-primary);
  font-size: 14px;
  width: 250px;
  transition: all 0.3s;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(0, 180, 216, 0.2);
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

/* 通知按钮 */
.notification-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 24px;
  cursor: pointer;
  position: relative;
  margin-right: 20px;
  transition: color 0.3s;
}

.notification-btn:hover {
  color: var(--primary);
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--accent);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 用户按钮 */
.user-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
}

.user-avatar-small {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 10px;
  color: white;
}

/* 下拉菜单 */
.user-dropdown-menu {
  background: var(--card-bg) !important;
  border: 1px solid var(--border) !important;
}

.dropdown-icon {
  margin-right: 8px;
  width: 16px;
}

/* 内容区域 */
.content-area {
  flex-grow: 1;
  padding: 30px;
  overflow-y: auto;
  background-color: var(--bg-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 80px;
  }

  .logo-text, .nav-text, .user-info, .nav-badge {
    display: none;
  }

  .logo {
    justify-content: center;
    padding: 25px 10px;
  }

  .logo-icon {
    margin-right: 0;
  }

  .nav-item {
    justify-content: center;
    padding: 18px 0;
  }

  .nav-icon {
    margin-right: 0;
    font-size: 24px;
  }

  .search-box input {
    width: 180px;
  }

  .top-bar {
    padding: 0 20px;
  }

  .content-area {
    padding: 20px;
  }
}
</style>
