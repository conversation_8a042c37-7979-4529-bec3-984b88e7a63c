<template>
  <div class="compose">
    <el-card class="compose-card">
      <template #header>
        <div class="card-header">
          <h2>{{ getTitle() }}</h2>
          <div class="header-actions">
            <el-button @click="saveDraft" :loading="savingDraft">
              <el-icon><Document /></el-icon>
              保存草稿
            </el-button>
            <el-button type="primary" @click="sendEmail" :loading="sending">
              <el-icon><Promotion /></el-icon>
              发送
            </el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="composeFormRef"
        :model="emailForm"
        :rules="emailRules"
        label-width="80px"
        class="compose-form"
      >
        <!-- 发件人 -->
        <el-form-item label="发件人" prop="from">
          <el-select
            v-model="emailForm.from"
            placeholder="选择发件邮箱"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="mailbox in userMailboxes"
              :key="mailbox.id"
              :label="mailbox.email"
              :value="mailbox.email"
            />
          </el-select>
        </el-form-item>

        <!-- 收件人 -->
        <el-form-item label="收件人" prop="to">
          <el-input
            v-model="emailForm.to"
            placeholder="输入收件人邮箱地址，多个地址用逗号分隔"
            type="textarea"
            :rows="2"
            resize="none"
          />
        </el-form-item>

        <!-- 抄送 -->
        <el-form-item v-if="showCc" label="抄送">
          <el-input
            v-model="emailForm.cc"
            placeholder="输入抄送邮箱地址，多个地址用逗号分隔"
            type="textarea"
            :rows="2"
            resize="none"
          />
        </el-form-item>

        <!-- 密送 -->
        <el-form-item v-if="showBcc" label="密送">
          <el-input
            v-model="emailForm.bcc"
            placeholder="输入密送邮箱地址，多个地址用逗号分隔"
            type="textarea"
            :rows="2"
            resize="none"
          />
        </el-form-item>

        <!-- 显示抄送/密送按钮 -->
        <div v-if="!showCc || !showBcc" class="cc-bcc-buttons">
          <el-button v-if="!showCc" text @click="showCc = true">
            + 抄送
          </el-button>
          <el-button v-if="!showBcc" text @click="showBcc = true">
            + 密送
          </el-button>
        </div>

        <!-- 主题 -->
        <el-form-item label="主题" prop="subject">
          <el-input
            v-model="emailForm.subject"
            placeholder="请输入邮件主题"
            clearable
          />
        </el-form-item>

        <!-- 邮件内容 -->
        <el-form-item label="内容" prop="content">
          <div class="editor-container">
            <div class="editor-toolbar">
              <el-button-group>
                <el-button
                  :type="emailForm.contentType === 'text' ? 'primary' : 'default'"
                  @click="emailForm.contentType = 'text'"
                >
                  纯文本
                </el-button>
                <el-button
                  :type="emailForm.contentType === 'html' ? 'primary' : 'default'"
                  @click="emailForm.contentType = 'html'"
                >
                  富文本
                </el-button>
              </el-button-group>
            </div>

            <el-input
              v-if="emailForm.contentType === 'text'"
              v-model="emailForm.content"
              type="textarea"
              placeholder="请输入邮件内容..."
              :rows="15"
              resize="vertical"
            />

            <div v-else class="html-editor">
              <el-input
                v-model="emailForm.content"
                type="textarea"
                placeholder="请输入HTML内容..."
                :rows="15"
                resize="vertical"
              />
            </div>
          </div>
        </el-form-item>

        <!-- 附件 -->
        <el-form-item label="附件">
          <div class="attachment-section">
            <el-upload
              ref="uploadRef"
              :file-list="attachments"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              multiple
              drag
              class="attachment-upload"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持多文件上传，单个文件不超过 25MB
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'

const route = useRoute()
const router = useRouter()

// 表单引用
const composeFormRef = ref()
const uploadRef = ref()

// 状态
const sending = ref(false)
const savingDraft = ref(false)
const showCc = ref(false)
const showBcc = ref(false)

// 用户邮箱列表
const userMailboxes = ref([])

// 附件列表
const attachments = ref([])

// 邮件表单
const emailForm = reactive({
  from: '',
  to: '',
  cc: '',
  bcc: '',
  subject: '',
  content: '',
  contentType: 'text'
})

// 表单验证规则
const emailRules = {
  from: [
    { required: true, message: '请选择发件邮箱', trigger: 'change' }
  ],
  to: [
    { required: true, message: '请输入收件人', trigger: 'blur' },
    { validator: validateEmails, trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入邮件主题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入邮件内容', trigger: 'blur' }
  ]
}

// 邮箱地址验证
function validateEmails(rule, value, callback) {
  if (!value) {
    callback()
    return
  }

  const emails = value.split(',').map(email => email.trim())
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  for (const email of emails) {
    if (email && !emailRegex.test(email)) {
      callback(new Error(`邮箱地址格式不正确: ${email}`))
      return
    }
  }

  callback()
}

// 获取页面标题
const getTitle = () => {
  const type = route.query.type
  switch (type) {
    case 'reply':
      return '回复邮件'
    case 'forward':
      return '转发邮件'
    default:
      return '写邮件'
  }
}

// 获取用户邮箱列表
const fetchUserMailboxes = async () => {
  try {
    const response = await api.get('/api/mailboxes')
    if (response.data.code === 200) {
      userMailboxes.value = response.data.data
      if (userMailboxes.value.length > 0) {
        emailForm.from = userMailboxes.value[0].email
      }
    }
  } catch (error) {
    console.error('获取邮箱列表失败:', error)
  }
}

// 处理文件变化
const handleFileChange = (file, fileList) => {
  // 检查文件大小
  const maxSize = 25 * 1024 * 1024 // 25MB
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过 25MB')
    return false
  }

  attachments.value = fileList
}

// 处理文件移除
const handleFileRemove = (file, fileList) => {
  attachments.value = fileList
}

// 发送邮件
const sendEmail = async () => {
  if (!composeFormRef.value) return

  try {
    const valid = await composeFormRef.value.validate()
    if (!valid) return

    sending.value = true

    // 准备表单数据
    const formData = new FormData()
    formData.append('from', emailForm.from)
    formData.append('to', emailForm.to)
    formData.append('cc', emailForm.cc)
    formData.append('bcc', emailForm.bcc)
    formData.append('subject', emailForm.subject)
    formData.append('content', emailForm.content)
    formData.append('content_type', emailForm.contentType)

    // 添加附件
    attachments.value.forEach(file => {
      if (file.raw) {
        formData.append('attachments', file.raw)
      }
    })

    const response = await api.post('/api/emails/send', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response.data.code === 200) {
      ElMessage.success('邮件发送成功')
      router.push('/sent')
    } else {
      ElMessage.error(response.data.message || '发送失败')
    }
  } catch (error) {
    console.error('发送邮件失败:', error)
    ElMessage.error('发送失败，请检查网络连接')
  } finally {
    sending.value = false
  }
}

// 保存草稿
const saveDraft = async () => {
  try {
    savingDraft.value = true

    const draftData = {
      from: emailForm.from,
      to: emailForm.to,
      cc: emailForm.cc,
      bcc: emailForm.bcc,
      subject: emailForm.subject,
      content: emailForm.content,
      content_type: emailForm.contentType
    }

    await api.post('/api/emails/draft', draftData)
    ElMessage.success('草稿保存成功')
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败')
  } finally {
    savingDraft.value = false
  }
}

// 加载原邮件数据（回复/转发）
const loadOriginalEmail = async () => {
  const type = route.query.type
  const emailId = route.query.id

  if (!type || !emailId) return

  try {
    const response = await api.get(`/api/emails/${emailId}`)
    if (response.data.code === 200) {
      const originalEmail = response.data.data

      if (type === 'reply') {
        emailForm.to = originalEmail.sender
        emailForm.subject = originalEmail.subject.startsWith('Re: ')
          ? originalEmail.subject
          : `Re: ${originalEmail.subject}`
        emailForm.content = `\n\n--- 原始邮件 ---\n发件人: ${originalEmail.sender}\n时间: ${originalEmail.created_at}\n主题: ${originalEmail.subject}\n\n${originalEmail.content}`
      } else if (type === 'forward') {
        emailForm.subject = originalEmail.subject.startsWith('Fwd: ')
          ? originalEmail.subject
          : `Fwd: ${originalEmail.subject}`
        emailForm.content = `\n\n--- 转发邮件 ---\n发件人: ${originalEmail.sender}\n收件人: ${originalEmail.recipient}\n时间: ${originalEmail.created_at}\n主题: ${originalEmail.subject}\n\n${originalEmail.content}`
      }
    }
  } catch (error) {
    console.error('加载原邮件失败:', error)
  }
}

onMounted(() => {
  fetchUserMailboxes()
  loadOriginalEmail()
})
</script>

<style scoped>
.compose {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.compose-card {
  min-height: 80vh;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.compose-form {
  padding: 20px 0;
}

.cc-bcc-buttons {
  margin: -10px 0 20px 80px;
  display: flex;
  gap: 15px;
}

.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  background: #f5f7fa;
  padding: 8px 12px;
  border-bottom: 1px solid #dcdfe6;
}

.html-editor {
  position: relative;
}

.attachment-section {
  width: 100%;
}

.attachment-upload {
  width: 100%;
}

.attachment-upload :deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
}

@media (max-width: 768px) {
  .compose {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .compose-form :deep(.el-form-item__label) {
    width: 60px !important;
  }

  .cc-bcc-buttons {
    margin-left: 60px;
  }
}
</style>
