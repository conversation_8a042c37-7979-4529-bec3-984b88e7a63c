<template>
  <div class="space-y-6">
    <!-- 页面标题和操作栏 -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gradient">收件箱</h1>
        <p class="text-slate-400 mt-1">{{ unreadCount }} 封未读邮件</p>
      </div>

      <div class="flex items-center space-x-3">
        <div class="relative">
          <font-awesome-icon icon="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索邮件..."
            class="input-glass pl-10 w-64"
          />
        </div>
        <button @click="refreshEmails" class="btn-secondary">
          <font-awesome-icon icon="sync-alt" :class="{ 'animate-spin': loading }" class="mr-2" />
          刷新
        </button>
        <button @click="composeEmail" class="btn-primary">
          <font-awesome-icon icon="edit" class="mr-2" />
          写邮件
        </button>
      </div>
    </div>

    <!-- 邮件列表 -->
    <div class="glass-card">
      <!-- 工具栏 -->
      <div class="flex items-center justify-between p-4 border-b border-slate-700">
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="selectAll"
              @change="toggleSelectAll"
              class="rounded border-slate-600 bg-slate-700 text-cyan-500 focus:ring-cyan-500"
            />
            <span class="ml-2 text-slate-300">全选</span>
          </label>

          <div v-if="selectedEmails.length > 0" class="flex items-center space-x-2">
            <button @click="markAsRead" class="text-slate-400 hover:text-white transition-colors">
              <font-awesome-icon icon="envelope-open" class="w-4 h-4" />
            </button>
            <button @click="deleteSelected" class="text-slate-400 hover:text-red-400 transition-colors">
              <font-awesome-icon icon="trash" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <div class="flex items-center space-x-2 text-slate-400 text-sm">
          <span>{{ filteredEmails.length }} 封邮件</span>
        </div>
      </div>

      <!-- 邮件列表内容 -->
      <div v-if="filteredEmails.length === 0" class="empty-state m-8">
        <font-awesome-icon icon="inbox" class="w-16 h-16 text-slate-500 mb-4" />
        <h3 class="text-lg font-medium text-slate-400 mb-2">收件箱为空</h3>
        <p class="text-slate-500">暂时没有收到新邮件</p>
      </div>

      <div v-else class="divide-y divide-slate-700">
        <div
          v-for="email in paginatedEmails"
          :key="email.id"
          :class="[
            'flex items-center p-4 hover:bg-slate-700/30 transition-colors cursor-pointer',
            !email.read && 'bg-slate-800/30'
          ]"
          @click="openEmail(email)"
        >
          <!-- 选择框 -->
          <div class="flex items-center mr-4">
            <input
              type="checkbox"
              v-model="selectedEmails"
              :value="email.id"
              @click.stop
              class="rounded border-slate-600 bg-slate-700 text-cyan-500 focus:ring-cyan-500"
            />
          </div>

          <!-- 发件人头像 -->
          <div class="w-10 h-10 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full flex items-center justify-center mr-4">
            <span class="text-white font-medium text-sm">{{ getInitials(email.sender) }}</span>
          </div>

          <!-- 邮件信息 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between mb-1">
              <p :class="['font-medium truncate', email.read ? 'text-slate-300' : 'text-white']">
                {{ email.sender }}
              </p>
              <div class="flex items-center space-x-2">
                <span class="text-slate-400 text-sm">{{ formatDate(email.date) }}</span>
                <div v-if="!email.read" class="w-2 h-2 bg-cyan-400 rounded-full"></div>
              </div>
            </div>
            <p :class="['truncate mb-1', email.read ? 'text-slate-400' : 'text-slate-200']">
              {{ email.subject }}
            </p>
            <p class="text-slate-500 text-sm truncate">{{ email.preview }}</p>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center space-x-2 ml-4 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              @click.stop="toggleRead(email)"
              class="p-2 text-slate-400 hover:text-white transition-colors"
              :title="email.read ? '标记为未读' : '标记为已读'"
            >
              <font-awesome-icon :icon="email.read ? 'envelope' : 'envelope-open'" class="w-4 h-4" />
            </button>
            <button
              @click.stop="deleteEmail(email)"
              class="p-2 text-slate-400 hover:text-red-400 transition-colors"
              title="删除"
            >
              <font-awesome-icon icon="trash" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="flex items-center justify-between p-4 border-t border-slate-700">
        <div class="text-slate-400 text-sm">
          显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredEmails.length) }}
          共 {{ filteredEmails.length }} 封邮件
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="currentPage--"
            :disabled="currentPage === 1"
            class="btn-secondary px-3 py-1 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          <span class="text-slate-300">{{ currentPage }} / {{ totalPages }}</span>
          <button
            @click="currentPage++"
            :disabled="currentPage === totalPages"
            class="btn-secondary px-3 py-1 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// import { ElMessage, ElMessageBox } from 'element-plus' // 已移除Element Plus

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const selectedEmails = ref([])
const selectAll = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)

// 模拟邮件数据
const emails = ref([
  {
    id: 1,
    sender: '<EMAIL>',
    subject: '欢迎使用Miko邮箱系统',
    preview: '感谢您注册Miko邮箱系统，这里是您的专属邮箱服务...',
    date: new Date(),
    read: false
  },
  {
    id: 2,
    sender: '<EMAIL>',
    subject: '系统维护通知',
    preview: '我们将在今晚进行系统维护，预计维护时间为2小时...',
    date: new Date(Date.now() - 86400000),
    read: true
  },
  {
    id: 3,
    sender: '<EMAIL>',
    subject: '功能更新说明',
    preview: '我们为您带来了全新的功能更新，包括更好的用户体验...',
    date: new Date(Date.now() - 172800000),
    read: false
  }
])

// 计算属性
const filteredEmails = computed(() => {
  if (!searchQuery.value) return emails.value
  return emails.value.filter(email =>
    email.sender.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    email.subject.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const unreadCount = computed(() => emails.value.filter(email => !email.read).length)

const totalPages = computed(() => Math.ceil(filteredEmails.value.length / pageSize.value))

const paginatedEmails = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredEmails.value.slice(start, end)
})

// 方法
const getInitials = (name) => {
  return name.split('@')[0].charAt(0).toUpperCase()
}

const formatDate = (date) => {
  const now = new Date()
  const emailDate = new Date(date)
  const diffTime = Math.abs(now - emailDate)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return emailDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (diffDays <= 7) {
    return `${diffDays}天前`
  } else {
    return emailDate.toLocaleDateString('zh-CN')
  }
}

const toggleSelectAll = () => {
  if (selectAll.value) {
    selectedEmails.value = paginatedEmails.value.map(email => email.id)
  } else {
    selectedEmails.value = []
  }
}

const refreshEmails = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    alert('邮件刷新成功')
  } catch (error) {
    alert('邮件刷新失败')
  } finally {
    loading.value = false
  }
}

const composeEmail = () => {
  router.push('/email/compose')
}

const openEmail = (email) => {
  router.push(`/email/detail/${email.id}`)
}

const toggleRead = (email) => {
  email.read = !email.read
  alert(email.read ? '已标记为已读' : '已标记为未读')
}

const deleteEmail = async (email) => {
  if (confirm('确定要删除这封邮件吗？')) {
    const index = emails.value.findIndex(e => e.id === email.id)
    if (index > -1) {
      emails.value.splice(index, 1)
      alert('邮件删除成功')
    }
  }
}

const markAsRead = () => {
  selectedEmails.value.forEach(emailId => {
    const email = emails.value.find(e => e.id === emailId)
    if (email) email.read = true
  })
  selectedEmails.value = []
  selectAll.value = false
  alert('已标记为已读')
}

const deleteSelected = async () => {
  if (confirm(`确定要删除选中的 ${selectedEmails.value.length} 封邮件吗？`)) {
    emails.value = emails.value.filter(email => !selectedEmails.value.includes(email.id))
    selectedEmails.value = []
    selectAll.value = false
    alert('邮件删除成功')
  }
}

onMounted(() => {
  // 初始化数据
})
</script>
