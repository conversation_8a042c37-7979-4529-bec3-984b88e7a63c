<template>
  <div class="admin-domains">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>域名管理</h2>
        <el-button @click="refreshDomains" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          添加域名
        </el-button>
      </div>
    </div>

    <!-- 域名列表 -->
    <el-card class="domains-list-card">
      <div v-if="loading && domains.length === 0" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="domains.length === 0" class="empty-state">
        <el-empty description="暂无域名" />
      </div>

      <div v-else>
        <el-table :data="domains" style="width: 100%">
          <el-table-column prop="name" label="域名" min-width="200" />
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="mx_record" label="MX记录" min-width="200" />
          
          <el-table-column prop="a_record" label="A记录" width="150" />
          
          <el-table-column prop="mailbox_count" label="邮箱数量" width="100" />
          
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="verifyDomain(row)">
                验证
              </el-button>
              <el-button size="small" @click="editDomain(row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="deleteDomain(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 创建/编辑域名对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingDomain ? '编辑域名' : '添加域名'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="域名" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入域名，如：example.com"
            :disabled="!!editingDomain"
          />
        </el-form-item>
        
        <el-form-item label="MX记录" prop="mxRecord">
          <el-input
            v-model="form.mxRecord"
            placeholder="请输入MX记录"
          />
        </el-form-item>
        
        <el-form-item label="A记录" prop="aRecord">
          <el-input
            v-model="form.aRecord"
            placeholder="请输入A记录IP地址"
          />
        </el-form-item>
        
        <el-form-item label="TXT记录">
          <el-input
            v-model="form.txtRecord"
            type="textarea"
            :rows="3"
            placeholder="请输入TXT记录（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveDomain" :loading="saving">
          {{ editingDomain ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const domains = ref([])
const showCreateDialog = ref(false)
const editingDomain = ref(null)

// 表单引用
const formRef = ref()

// 表单数据
const form = reactive({
  name: '',
  mxRecord: '',
  aRecord: '',
  txtRecord: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入域名', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入正确的域名格式', trigger: 'blur' }
  ],
  mxRecord: [
    { required: true, message: '请输入MX记录', trigger: 'blur' }
  ],
  aRecord: [
    { required: true, message: '请输入A记录', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入正确的IP地址', trigger: 'blur' }
  ]
}

// 获取域名列表
const fetchDomains = async () => {
  try {
    loading.value = true
    const response = await api.get('/api/admin/domains')
    if (response.data.code === 200) {
      domains.value = response.data.data
    }
  } catch (error) {
    console.error('获取域名列表失败:', error)
    ElMessage.error('获取域名列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新域名列表
const refreshDomains = () => {
  fetchDomains()
}

// 格式化日期时间
const formatDateTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'verified':
      return 'success'
    case 'pending':
      return 'warning'
    case 'failed':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'verified':
      return '已验证'
    case 'pending':
      return '待验证'
    case 'failed':
      return '验证失败'
    default:
      return '未知'
  }
}

// 编辑域名
const editDomain = (domain) => {
  editingDomain.value = domain
  form.name = domain.name
  form.mxRecord = domain.mx_record
  form.aRecord = domain.a_record
  form.txtRecord = domain.txt_record || ''
  showCreateDialog.value = true
}

// 保存域名
const saveDomain = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    saving.value = true
    
    const data = {
      name: form.name,
      mx_record: form.mxRecord,
      a_record: form.aRecord,
      txt_record: form.txtRecord
    }
    
    let response
    if (editingDomain.value) {
      response = await api.put(`/api/admin/domains/${editingDomain.value.id}`, data)
    } else {
      response = await api.post('/api/admin/domains', data)
    }
    
    if (response.data.code === 200) {
      ElMessage.success(editingDomain.value ? '域名更新成功' : '域名创建成功')
      showCreateDialog.value = false
      fetchDomains()
    } else {
      ElMessage.error(response.data.message || '操作失败')
    }
  } catch (error) {
    console.error('保存域名失败:', error)
    ElMessage.error('操作失败')
  } finally {
    saving.value = false
  }
}

// 验证域名
const verifyDomain = async (domain) => {
  try {
    await api.post(`/api/admin/domains/${domain.id}/verify`)
    ElMessage.success('域名验证已开始，请稍后查看结果')
    fetchDomains()
  } catch (error) {
    console.error('验证域名失败:', error)
    ElMessage.error('验证失败')
  }
}

// 删除域名
const deleteDomain = async (domain) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除域名 ${domain.name} 吗？删除后无法恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.delete(`/api/admin/domains/${domain.id}`)
    ElMessage.success('域名删除成功')
    fetchDomains()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除域名失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 重置表单
const resetForm = () => {
  editingDomain.value = null
  form.name = ''
  form.mxRecord = ''
  form.aRecord = ''
  form.txtRecord = ''
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  fetchDomains()
})
</script>

<style scoped>
.admin-domains {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.toolbar-left h2 {
  margin: 0;
  color: #333;
}

.domains-list-card {
  min-height: 600px;
}

.loading-state,
.empty-state {
  padding: 40px 0;
}

@media (max-width: 768px) {
  .admin-domains {
    padding: 10px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
}
</style>
