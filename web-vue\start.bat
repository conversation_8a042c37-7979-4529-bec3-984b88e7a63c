@echo off
echo ========================================
echo    Miko邮箱系统 Vue前端启动脚本
echo ========================================
echo.

echo 检查Node.js版本...
node --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo.
echo 检查npm版本...
npm --version
if %errorlevel% neq 0 (
    echo 错误: 未找到npm
    pause
    exit /b 1
)

echo.
echo 检查依赖是否已安装...
if not exist "node_modules" (
    echo 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖已安装，跳过安装步骤
)

echo.
echo 启动开发服务器...
echo 请在浏览器中访问: http://localhost:3000
echo 按 Ctrl+C 停止服务器
echo.
npm run dev

pause
