{"version": 3, "file": "useSelect.mjs", "sources": ["../../../../../../packages/components/select-v2/src/useSelect.ts"], "sourcesContent": ["import {\n  computed,\n  nextTick,\n  onMounted,\n  reactive,\n  ref,\n  toRaw,\n  watch,\n  watchEffect,\n} from 'vue'\nimport {\n  findLastIndex,\n  get,\n  isEqual,\n  debounce as lodashDebounce,\n} from 'lodash-unified'\nimport { useResizeObserver } from '@vueuse/core'\nimport {\n  ValidateComponentsMap,\n  debugWarn,\n  escapeStringRegexp,\n  isArray,\n  isFunction,\n  isNumber,\n  isObject,\n  isUndefined,\n} from '@element-plus/utils'\nimport {\n  useComposition,\n  useEmptyValues,\n  useFocusController,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport {\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { useAllowCreate } from './useAllowCreate'\nimport { useProps } from './useProps'\n\nimport type { Option, OptionType, SelectStates } from './select.types'\nimport type { SelectV2Props } from './token'\nimport type { SelectV2EmitFn } from './defaults'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { SelectDropdownInstance } from './select-dropdown'\n\nconst useSelect = (props: SelectV2Props, emit: SelectV2EmitFn) => {\n  // inject\n  const { t } = useLocale()\n  const nsSelect = useNamespace('select')\n  const nsInput = useNamespace('input')\n  const { form: elForm, formItem: elFormItem } = useFormItem()\n  const { inputId } = useFormItemInputId(props, {\n    formItemContext: elFormItem,\n  })\n  const { aliasProps, getLabel, getValue, getDisabled, getOptions } =\n    useProps(props)\n  const { valueOnClear, isEmptyValue } = useEmptyValues(props)\n\n  const states: SelectStates = reactive({\n    inputValue: '',\n    cachedOptions: [],\n    createdOptions: [],\n    hoveringIndex: -1,\n    inputHovering: false,\n    selectionWidth: 0,\n    collapseItemWidth: 0,\n    previousQuery: null,\n    previousValue: undefined,\n    selectedLabel: '',\n    menuVisibleOnFocus: false,\n    isBeforeHide: false,\n  })\n\n  // data refs\n  const popperSize = ref(-1)\n\n  // DOM & Component refs\n  const selectRef = ref<HTMLElement>()\n  const selectionRef = ref<HTMLElement>()\n  const tooltipRef = ref<TooltipInstance>()\n  const tagTooltipRef = ref<TooltipInstance>()\n  const inputRef = ref<HTMLElement>()\n  const prefixRef = ref<HTMLElement>()\n  const suffixRef = ref<HTMLElement>()\n  const menuRef = ref<SelectDropdownInstance>()\n  const tagMenuRef = ref<HTMLElement>()\n  const collapseItemRef = ref<HTMLElement>()\n\n  const {\n    isComposing,\n    handleCompositionStart,\n    handleCompositionEnd,\n    handleCompositionUpdate,\n  } = useComposition({\n    afterComposition: (e) => onInput(e),\n  })\n\n  const selectDisabled = computed(() => props.disabled || !!elForm?.disabled)\n\n  const { wrapperRef, isFocused, handleBlur } = useFocusController(inputRef, {\n    disabled: selectDisabled,\n    afterFocus() {\n      if (props.automaticDropdown && !expanded.value) {\n        expanded.value = true\n        states.menuVisibleOnFocus = true\n      }\n    },\n    beforeBlur(event) {\n      return (\n        tooltipRef.value?.isFocusInsideContent(event) ||\n        tagTooltipRef.value?.isFocusInsideContent(event)\n      )\n    },\n    afterBlur() {\n      expanded.value = false\n      states.menuVisibleOnFocus = false\n      if (props.validateEvent) {\n        elFormItem?.validate?.('blur').catch((err) => debugWarn(err))\n      }\n    },\n  })\n\n  const allOptions = computed(() => filterOptions(''))\n\n  const hasOptions = computed(() => {\n    if (props.loading) return false\n    return props.options.length > 0 || states.createdOptions.length > 0\n  })\n\n  const filteredOptions = ref<OptionType[]>([])\n  // the controller of the expanded popup\n  const expanded = ref(false)\n\n  const needStatusIcon = computed(() => elForm?.statusIcon ?? false)\n\n  const popupHeight = computed(() => {\n    const totalHeight = filteredOptions.value.length * props.itemHeight\n    return totalHeight > props.height ? props.height : totalHeight\n  })\n\n  const hasModelValue = computed(() => {\n    return props.multiple\n      ? isArray(props.modelValue) && props.modelValue.length > 0\n      : !isEmptyValue(props.modelValue)\n  })\n\n  const showClearBtn = computed(() => {\n    return (\n      props.clearable &&\n      !selectDisabled.value &&\n      states.inputHovering &&\n      hasModelValue.value\n    )\n  })\n\n  const iconComponent = computed(() =>\n    props.remote && props.filterable ? '' : props.suffixIcon\n  )\n\n  const iconReverse = computed(\n    () => iconComponent.value && nsSelect.is('reverse', expanded.value)\n  )\n\n  const validateState = computed(() => elFormItem?.validateState || '')\n  const validateIcon = computed(() => {\n    // When we use indexed access to get the type of an undeclared property,\n    // the unsafe type `any` will be inferred, which TypeScript throws an error to emphasize it.\n    // To avoid TypeScript complaining about it, we use truthiness narrowing to narrow the type of validateState.\n    if (!validateState.value) return\n    return ValidateComponentsMap[validateState.value]\n  })\n\n  const debounce = computed(() => (props.remote ? 300 : 0))\n\n  // filteredOptions includes flatten the data into one dimensional array.\n  const emptyText = computed(() => {\n    if (props.loading) {\n      return props.loadingText || t('el.select.loading')\n    } else {\n      if (props.remote && !states.inputValue && !hasOptions.value) return false\n      if (\n        props.filterable &&\n        states.inputValue &&\n        hasOptions.value &&\n        filteredOptions.value.length === 0\n      ) {\n        return props.noMatchText || t('el.select.noMatch')\n      }\n      if (!hasOptions.value) {\n        return props.noDataText || t('el.select.noData')\n      }\n    }\n    return null\n  })\n\n  const filterOptions = (query: string) => {\n    const regexp = new RegExp(escapeStringRegexp(query), 'i')\n    const { filterMethod, remoteMethod } = toRaw(props)\n    const isFilterMethodValid = props.filterable && isFunction(filterMethod)\n    const isRemoteMethodValid =\n      props.filterable && props.remote && isFunction(remoteMethod)\n    const isValidOption = (o: Option): boolean => {\n      if (isFilterMethodValid || isRemoteMethodValid) return true\n      // when query was given, we should test on the label see whether the label contains the given query\n      return query ? regexp.test(getLabel(o) || '') : true\n    }\n    if (props.loading) {\n      return []\n    }\n\n    return [...states.createdOptions, ...props.options].reduce((all, item) => {\n      const options = getOptions(item)\n\n      if (isArray(options)) {\n        const filtered = options.filter(isValidOption)\n\n        if (filtered.length > 0) {\n          all.push(\n            {\n              label: getLabel(item),\n              type: 'Group',\n            },\n            ...filtered\n          )\n        }\n      } else if (props.remote || isValidOption(item)) {\n        all.push(item)\n      }\n\n      return all\n    }, []) as OptionType[]\n  }\n\n  const updateOptions = () => {\n    filteredOptions.value = filterOptions(states.inputValue)\n  }\n\n  const allOptionsValueMap = computed(() => {\n    const valueMap = new Map()\n\n    allOptions.value.forEach((option, index) => {\n      valueMap.set(getValueKey(getValue(option)), { option, index })\n    })\n    return valueMap\n  })\n\n  const filteredOptionsValueMap = computed(() => {\n    const valueMap = new Map()\n\n    filteredOptions.value.forEach((option, index) => {\n      valueMap.set(getValueKey(getValue(option)), { option, index })\n    })\n    return valueMap\n  })\n\n  const optionsAllDisabled = computed(() =>\n    filteredOptions.value.every((option) => getDisabled(option))\n  )\n\n  const selectSize = useFormSize()\n\n  const collapseTagSize = computed(() =>\n    'small' === selectSize.value ? 'small' : 'default'\n  )\n\n  const calculatePopperSize = () => {\n    if (isNumber(props.fitInputWidth)) {\n      popperSize.value = props.fitInputWidth\n      return\n    }\n    const width = selectRef.value?.offsetWidth || 200\n    if (!props.fitInputWidth && hasOptions.value) {\n      nextTick(() => {\n        popperSize.value = Math.max(width, calculateLabelMaxWidth())\n      })\n    } else {\n      popperSize.value = width\n    }\n  }\n\n  // TODO Caching implementation\n  // 1. There is no need to calculate options that have already been calculated\n  // 2. Repeatedly expand and close when persistent is set to false, no need for repeated calculations\n  const calculateLabelMaxWidth = () => {\n    const canvas = document.createElement('canvas')\n    const ctx = canvas.getContext('2d')\n    const selector = nsSelect.be('dropdown', 'item')\n    const dom = menuRef.value?.listRef?.innerRef || document\n    const dropdownItemEl = dom.querySelector(`.${selector}`)\n    if (dropdownItemEl === null || ctx === null) return 0\n    const style = getComputedStyle(dropdownItemEl)\n    const padding =\n      Number.parseFloat(style.paddingLeft) +\n      Number.parseFloat(style.paddingRight)\n    ctx.font = `bold ${style.font.replace(\n      new RegExp(`\\\\b${style.fontWeight}\\\\b`),\n      ''\n    )}`\n    const maxWidth = filteredOptions.value.reduce((max, option) => {\n      const metrics = ctx.measureText(getLabel(option))\n      return Math.max(metrics.width, max)\n    }, 0)\n    return maxWidth + padding\n  }\n\n  const getGapWidth = () => {\n    if (!selectionRef.value) return 0\n    const style = window.getComputedStyle(selectionRef.value)\n    return Number.parseFloat(style.gap || '6px')\n  }\n\n  // computed style\n  const tagStyle = computed(() => {\n    const gapWidth = getGapWidth()\n    const maxWidth =\n      collapseItemRef.value && props.maxCollapseTags === 1\n        ? states.selectionWidth - states.collapseItemWidth - gapWidth\n        : states.selectionWidth\n    return { maxWidth: `${maxWidth}px` }\n  })\n\n  const collapseTagStyle = computed(() => {\n    return { maxWidth: `${states.selectionWidth}px` }\n  })\n\n  const shouldShowPlaceholder = computed(() => {\n    if (isArray(props.modelValue)) {\n      return props.modelValue.length === 0 && !states.inputValue\n    }\n\n    // when it's not multiple mode, we only determine this flag based on filterable and expanded\n    // when filterable flag is true, which means we have input box on the screen\n    return props.filterable ? !states.inputValue : true\n  })\n\n  const currentPlaceholder = computed(() => {\n    const _placeholder = props.placeholder ?? t('el.select.placeholder')\n    return props.multiple || !hasModelValue.value\n      ? _placeholder\n      : states.selectedLabel\n  })\n\n  // this obtains the actual popper DOM element.\n  const popperRef = computed(() => tooltipRef.value?.popperRef?.contentRef)\n\n  // the index with current value in options\n  const indexRef = computed<number>(() => {\n    if (props.multiple) {\n      const len = (props.modelValue as []).length\n      if (\n        (props.modelValue as Array<any>).length > 0 &&\n        filteredOptionsValueMap.value.has(props.modelValue[len - 1])\n      ) {\n        const { index } = filteredOptionsValueMap.value.get(\n          props.modelValue[len - 1]\n        )\n        return index\n      }\n    } else {\n      if (\n        !isEmptyValue(props.modelValue) &&\n        filteredOptionsValueMap.value.has(props.modelValue)\n      ) {\n        const { index } = filteredOptionsValueMap.value.get(props.modelValue)\n        return index\n      }\n    }\n    return -1\n  })\n\n  const dropdownMenuVisible = computed({\n    get() {\n      return expanded.value && emptyText.value !== false\n    },\n    set(val: boolean) {\n      expanded.value = val\n    },\n  })\n\n  const showTagList = computed(() => {\n    if (!props.multiple) {\n      return []\n    }\n    return props.collapseTags\n      ? states.cachedOptions.slice(0, props.maxCollapseTags)\n      : states.cachedOptions\n  })\n\n  const collapseTagList = computed(() => {\n    if (!props.multiple) {\n      return []\n    }\n    return props.collapseTags\n      ? states.cachedOptions.slice(props.maxCollapseTags)\n      : []\n  })\n\n  // hooks\n  const {\n    createNewOption,\n    removeNewOption,\n    selectNewOption,\n    clearAllNewOption,\n  } = useAllowCreate(props, states)\n\n  // methods\n  const toggleMenu = () => {\n    if (selectDisabled.value) return\n\n    if (states.menuVisibleOnFocus) {\n      // controlled by automaticDropdown\n      states.menuVisibleOnFocus = false\n    } else {\n      expanded.value = !expanded.value\n    }\n  }\n\n  const onInputChange = () => {\n    if (states.inputValue.length > 0 && !expanded.value) {\n      expanded.value = true\n    }\n    createNewOption(states.inputValue)\n    nextTick(() => {\n      handleQueryChange(states.inputValue)\n    })\n  }\n\n  const debouncedOnInputChange = lodashDebounce(onInputChange, debounce.value)\n\n  const handleQueryChange = (val: string) => {\n    if (states.previousQuery === val || isComposing.value) {\n      return\n    }\n    states.previousQuery = val\n    if (props.filterable && isFunction(props.filterMethod)) {\n      props.filterMethod(val)\n    } else if (\n      props.filterable &&\n      props.remote &&\n      isFunction(props.remoteMethod)\n    ) {\n      props.remoteMethod(val)\n    }\n    if (\n      props.defaultFirstOption &&\n      (props.filterable || props.remote) &&\n      filteredOptions.value.length\n    ) {\n      nextTick(checkDefaultFirstOption)\n    } else {\n      nextTick(updateHoveringIndex)\n    }\n  }\n\n  /**\n   * find and highlight first option as default selected\n   * @remark\n   * - if the first option in dropdown list is user-created,\n   *   it would be at the end of the optionsArray\n   *   so find it and set hover.\n   *   (NOTE: there must be only one user-created option in dropdown list with query)\n   * - if there's no user-created option in list, just find the first one as usual\n   *   (NOTE: exclude options that are disabled or in disabled-group)\n   */\n  const checkDefaultFirstOption = () => {\n    const optionsInDropdown = filteredOptions.value.filter(\n      (n) => !n.disabled && n.type !== 'Group'\n    )\n    const userCreatedOption = optionsInDropdown.find((n) => n.created)\n    const firstOriginOption = optionsInDropdown[0]\n    states.hoveringIndex = getValueIndex(\n      filteredOptions.value,\n      userCreatedOption || firstOriginOption\n    )\n  }\n\n  const emitChange = (val: any | any[]) => {\n    if (!isEqual(props.modelValue, val)) {\n      emit(CHANGE_EVENT, val)\n    }\n  }\n\n  const update = (val: any) => {\n    emit(UPDATE_MODEL_EVENT, val)\n    emitChange(val)\n    states.previousValue = props.multiple ? String(val) : val\n\n    nextTick(() => {\n      if (props.multiple && isArray(props.modelValue)) {\n        const cachedOptions = states.cachedOptions.slice()\n        const selectedOptions = props.modelValue.map((value) =>\n          getOption(value, cachedOptions)\n        )\n\n        if (!isEqual(states.cachedOptions, selectedOptions)) {\n          states.cachedOptions = selectedOptions\n        }\n      } else {\n        initStates(true)\n      }\n    })\n  }\n\n  const getValueIndex = (arr: unknown[] = [], value: unknown) => {\n    if (!isObject(value)) {\n      return arr.indexOf(value)\n    }\n    const valueKey = props.valueKey\n    let index = -1\n    arr.some((item, i) => {\n      if (get(item, valueKey) === get(value, valueKey)) {\n        index = i\n        return true\n      }\n      return false\n    })\n    return index\n  }\n\n  const getValueKey = (item: unknown) => {\n    return isObject(item) ? get(item, props.valueKey) : item\n  }\n\n  const handleResize = () => {\n    calculatePopperSize()\n  }\n\n  const resetSelectionWidth = () => {\n    states.selectionWidth = Number.parseFloat(\n      window.getComputedStyle(selectionRef.value!).width\n    )\n  }\n\n  const resetCollapseItemWidth = () => {\n    states.collapseItemWidth =\n      collapseItemRef.value!.getBoundingClientRect().width\n  }\n\n  const updateTooltip = () => {\n    tooltipRef.value?.updatePopper?.()\n  }\n\n  const updateTagTooltip = () => {\n    tagTooltipRef.value?.updatePopper?.()\n  }\n\n  const onSelect = (option: Option) => {\n    if (props.multiple) {\n      let selectedOptions = (props.modelValue as any[]).slice()\n\n      const index = getValueIndex(selectedOptions, getValue(option))\n      if (index > -1) {\n        selectedOptions = [\n          ...selectedOptions.slice(0, index),\n          ...selectedOptions.slice(index + 1),\n        ]\n        states.cachedOptions.splice(index, 1)\n        removeNewOption(option)\n      } else if (\n        props.multipleLimit <= 0 ||\n        selectedOptions.length < props.multipleLimit\n      ) {\n        selectedOptions = [...selectedOptions, getValue(option)]\n        states.cachedOptions.push(option)\n        selectNewOption(option)\n      }\n      update(selectedOptions)\n      if (option.created) {\n        handleQueryChange('')\n      }\n      if (props.filterable && !props.reserveKeyword) {\n        states.inputValue = ''\n      }\n    } else {\n      states.selectedLabel = getLabel(option)\n      update(getValue(option))\n      expanded.value = false\n      selectNewOption(option)\n      if (!option.created) {\n        clearAllNewOption()\n      }\n    }\n    focus()\n  }\n\n  const deleteTag = (event: MouseEvent, option: Option) => {\n    let selectedOptions = (props.modelValue as any[]).slice()\n\n    const index = getValueIndex(selectedOptions, getValue(option))\n\n    if (index > -1 && !selectDisabled.value) {\n      selectedOptions = [\n        ...(props.modelValue as Array<unknown>).slice(0, index),\n        ...(props.modelValue as Array<unknown>).slice(index + 1),\n      ]\n      states.cachedOptions.splice(index, 1)\n      update(selectedOptions)\n      emit('remove-tag', getValue(option))\n      removeNewOption(option)\n    }\n    event.stopPropagation()\n    focus()\n  }\n\n  const focus = () => {\n    inputRef.value?.focus()\n  }\n\n  const blur = () => {\n    if (expanded.value) {\n      expanded.value = false\n      nextTick(() => inputRef.value?.blur())\n      return\n    }\n    inputRef.value?.blur()\n  }\n\n  // keyboard handlers\n  const handleEsc = () => {\n    if (states.inputValue.length > 0) {\n      states.inputValue = ''\n    } else {\n      expanded.value = false\n    }\n  }\n\n  const getLastNotDisabledIndex = (value: unknown[]) =>\n    findLastIndex(\n      value,\n      (it) =>\n        !states.cachedOptions.some(\n          (option) => getValue(option) === it && getDisabled(option)\n        )\n    )\n\n  const handleDel = (e: KeyboardEvent) => {\n    if (!props.multiple) return\n    if (e.code === EVENT_CODE.delete) return\n    if (states.inputValue.length === 0) {\n      e.preventDefault()\n      const selected = (props.modelValue as Array<any>).slice()\n      const lastNotDisabledIndex = getLastNotDisabledIndex(selected)\n      if (lastNotDisabledIndex < 0) return\n      const removeTagValue = selected[lastNotDisabledIndex]\n      selected.splice(lastNotDisabledIndex, 1)\n      const option = states.cachedOptions[lastNotDisabledIndex]\n      states.cachedOptions.splice(lastNotDisabledIndex, 1)\n      removeNewOption(option)\n      update(selected)\n      emit('remove-tag', removeTagValue)\n    }\n  }\n\n  const handleClear = () => {\n    let emptyValue: string | any[]\n    if (isArray(props.modelValue)) {\n      emptyValue = []\n    } else {\n      emptyValue = valueOnClear.value\n    }\n\n    states.selectedLabel = ''\n\n    expanded.value = false\n    update(emptyValue)\n    emit('clear')\n    clearAllNewOption()\n    focus()\n  }\n\n  const onKeyboardNavigate = (\n    direction: 'forward' | 'backward',\n    hoveringIndex: number | undefined = undefined\n  ): void => {\n    const options = filteredOptions.value\n    if (\n      !['forward', 'backward'].includes(direction) ||\n      selectDisabled.value ||\n      options.length <= 0 ||\n      optionsAllDisabled.value ||\n      isComposing.value\n    ) {\n      return\n    }\n    if (!expanded.value) {\n      return toggleMenu()\n    }\n    if (isUndefined(hoveringIndex)) {\n      hoveringIndex = states.hoveringIndex\n    }\n    let newIndex = -1\n    if (direction === 'forward') {\n      newIndex = hoveringIndex + 1\n      if (newIndex >= options.length) {\n        // return to the first option\n        newIndex = 0\n      }\n    } else if (direction === 'backward') {\n      newIndex = hoveringIndex - 1\n      if (newIndex < 0 || newIndex >= options.length) {\n        // navigate to the last one\n        newIndex = options.length - 1\n      }\n    }\n    const option = options[newIndex]\n    if (getDisabled(option) || option.type === 'Group') {\n      // prevent dispatching multiple nextTick callbacks.\n      return onKeyboardNavigate(direction, newIndex)\n    } else {\n      states.hoveringIndex = newIndex\n      scrollToItem(newIndex)\n    }\n  }\n\n  const onKeyboardSelect = () => {\n    if (!expanded.value) {\n      return toggleMenu()\n    } else if (\n      ~states.hoveringIndex &&\n      filteredOptions.value[states.hoveringIndex]\n    ) {\n      onSelect(filteredOptions.value[states.hoveringIndex])\n    }\n  }\n\n  const onHoverOption = (idx?: number) => {\n    states.hoveringIndex = idx ?? -1\n  }\n\n  const updateHoveringIndex = () => {\n    if (!props.multiple) {\n      states.hoveringIndex = filteredOptions.value.findIndex((item) => {\n        return getValueKey(getValue(item)) === getValueKey(props.modelValue)\n      })\n    } else {\n      states.hoveringIndex = filteredOptions.value.findIndex((item) =>\n        props.modelValue.some(\n          (modelValue: unknown) =>\n            getValueKey(modelValue) === getValueKey(getValue(item))\n        )\n      )\n    }\n  }\n\n  const onInput = (event: Event) => {\n    states.inputValue = (event.target as HTMLInputElement).value\n    if (props.remote) {\n      debouncedOnInputChange()\n    } else {\n      return onInputChange()\n    }\n  }\n\n  const handleClickOutside = (event: Event) => {\n    expanded.value = false\n\n    if (isFocused.value) {\n      const _event = new FocusEvent('focus', event)\n      handleBlur(_event)\n    }\n  }\n\n  const handleMenuEnter = () => {\n    states.isBeforeHide = false\n    return nextTick(() => {\n      if (~indexRef.value) {\n        scrollToItem(states.hoveringIndex)\n      }\n    })\n  }\n\n  const scrollToItem = (index: number) => {\n    menuRef.value!.scrollToItem(index)\n  }\n\n  const getOption = (value: unknown, cachedOptions?: Option[]) => {\n    // match the option with the given value, if not found, create a new option\n    const selectValue = getValueKey(value)\n\n    if (allOptionsValueMap.value.has(selectValue)) {\n      const { option } = allOptionsValueMap.value.get(selectValue)\n\n      return option\n    }\n    if (cachedOptions && cachedOptions.length) {\n      const option = cachedOptions.find(\n        (option) => getValueKey(getValue(option)) === selectValue\n      )\n      if (option) {\n        return option\n      }\n    }\n\n    return {\n      [aliasProps.value.value]: value,\n      [aliasProps.value.label]: value,\n    }\n  }\n\n  const initStates = (needUpdateSelectedLabel = false) => {\n    if (props.multiple) {\n      if ((props.modelValue as Array<any>).length > 0) {\n        const cachedOptions = states.cachedOptions.slice()\n        states.cachedOptions.length = 0\n        states.previousValue = props.modelValue.toString()\n\n        for (const value of props.modelValue) {\n          const option = getOption(value, cachedOptions)\n          states.cachedOptions.push(option)\n        }\n      } else {\n        states.cachedOptions = []\n        states.previousValue = undefined\n      }\n    } else {\n      if (hasModelValue.value) {\n        states.previousValue = props.modelValue\n        const options = filteredOptions.value\n        const selectedItemIndex = options.findIndex(\n          (option) =>\n            getValueKey(getValue(option)) === getValueKey(props.modelValue)\n        )\n        if (~selectedItemIndex) {\n          states.selectedLabel = getLabel(options[selectedItemIndex])\n        } else {\n          if (!states.selectedLabel || needUpdateSelectedLabel) {\n            states.selectedLabel = getValueKey(props.modelValue)\n          }\n        }\n      } else {\n        states.selectedLabel = ''\n        states.previousValue = undefined\n      }\n    }\n    clearAllNewOption()\n    calculatePopperSize()\n  }\n\n  watch(\n    () => props.fitInputWidth,\n    () => {\n      calculatePopperSize()\n    }\n  )\n\n  // in order to track these individually, we need to turn them into refs instead of watching the entire\n  // reactive object which could cause perf penalty when unnecessary field gets changed the watch method will\n  // be invoked.\n\n  watch(expanded, (val) => {\n    if (val) {\n      if (!props.persistent) {\n        calculatePopperSize()\n      }\n      handleQueryChange('')\n    } else {\n      states.inputValue = ''\n      states.previousQuery = null\n      states.isBeforeHide = true\n      createNewOption('')\n    }\n    emit('visible-change', val)\n  })\n\n  watch(\n    () => props.modelValue,\n    (val, oldVal) => {\n      const isValEmpty = !val || (isArray(val) && val.length === 0)\n\n      if (\n        isValEmpty ||\n        (props.multiple && !isEqual(val.toString(), states.previousValue)) ||\n        (!props.multiple &&\n          getValueKey(val) !== getValueKey(states.previousValue))\n      ) {\n        initStates(true)\n      }\n      if (!isEqual(val, oldVal) && props.validateEvent) {\n        elFormItem?.validate?.('change').catch((err) => debugWarn(err))\n      }\n    },\n    {\n      deep: true,\n    }\n  )\n\n  watch(\n    () => props.options,\n    () => {\n      const input = inputRef.value\n      // filter or remote-search scenarios are not initialized\n      if (!input || (input && document.activeElement !== input)) {\n        initStates()\n      }\n    },\n    {\n      deep: true,\n      flush: 'post',\n    }\n  )\n\n  // fix the problem that scrollTop is not reset in filterable mode\n  watch(\n    () => filteredOptions.value,\n    () => {\n      calculatePopperSize()\n      return menuRef.value && nextTick(menuRef.value.resetScrollTop)\n    }\n  )\n\n  watchEffect(() => {\n    // Anything could cause options changed, then update options\n    // If you want to control it by condition, write here\n    if (states.isBeforeHide) return\n    updateOptions()\n  })\n\n  watchEffect(() => {\n    const { valueKey, options } = props\n    const duplicateValue = new Map()\n    for (const item of options) {\n      const optionValue = getValue(item)\n      let v = optionValue\n      if (isObject(v)) {\n        v = get(optionValue, valueKey)\n      }\n      if (duplicateValue.get(v)) {\n        debugWarn(\n          'ElSelectV2',\n          `The option values you provided seem to be duplicated, which may cause some problems, please check.`\n        )\n        break\n      } else {\n        duplicateValue.set(v, true)\n      }\n    }\n  })\n\n  onMounted(() => {\n    initStates()\n  })\n  useResizeObserver(selectRef, handleResize)\n  useResizeObserver(selectionRef, resetSelectionWidth)\n  useResizeObserver(menuRef, updateTooltip)\n  useResizeObserver(wrapperRef, updateTooltip)\n  useResizeObserver(tagMenuRef, updateTagTooltip)\n  useResizeObserver(collapseItemRef, resetCollapseItemWidth)\n\n  return {\n    // data exports\n    inputId,\n    collapseTagSize,\n    currentPlaceholder,\n    expanded,\n    emptyText,\n    popupHeight,\n    debounce,\n    allOptions,\n    filteredOptions,\n    iconComponent,\n    iconReverse,\n    tagStyle,\n    collapseTagStyle,\n    popperSize,\n    dropdownMenuVisible,\n    hasModelValue,\n    shouldShowPlaceholder,\n    selectDisabled,\n    selectSize,\n    needStatusIcon,\n    showClearBtn,\n    states,\n    isFocused,\n    nsSelect,\n    nsInput,\n\n    // refs items exports\n    inputRef,\n    menuRef,\n    tagMenuRef,\n    tooltipRef,\n    tagTooltipRef,\n    selectRef,\n    wrapperRef,\n    selectionRef,\n    prefixRef,\n    suffixRef,\n    collapseItemRef,\n\n    popperRef,\n\n    validateState,\n    validateIcon,\n    showTagList,\n    collapseTagList,\n\n    // methods exports\n    debouncedOnInputChange,\n    deleteTag,\n    getLabel,\n    getValue,\n    getDisabled,\n    getValueKey,\n    handleClear,\n    handleClickOutside,\n    handleDel,\n    handleEsc,\n    focus,\n    blur,\n    handleMenuEnter,\n    handleResize,\n    resetSelectionWidth,\n    updateTooltip,\n    updateTagTooltip,\n    updateOptions,\n    toggleMenu,\n    scrollTo: scrollToItem,\n    onInput,\n    onKeyboardNavigate,\n    onKeyboardSelect,\n    onSelect,\n    onHover: onHoverOption,\n    handleCompositionStart,\n    handleCompositionEnd,\n    handleCompositionUpdate,\n  }\n}\n\nexport default useSelect\n"], "names": ["debounce", "lodashDebounce"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA8CK,MAAC,SAAS,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AACnC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AAC5B,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC1C,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACxC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,WAAW,EAAE,CAAC;AAC/D,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,kBAAkB,CAAC,KAAK,EAAE;AAChD,IAAI,eAAe,EAAE,UAAU;AAC/B,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACtF,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/D,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC;AAC1B,IAAI,UAAU,EAAE,EAAE;AAClB,IAAI,aAAa,EAAE,EAAE;AACrB,IAAI,cAAc,EAAE,EAAE;AACtB,IAAI,aAAa,EAAE,CAAC,CAAC;AACrB,IAAI,aAAa,EAAE,KAAK;AACxB,IAAI,cAAc,EAAE,CAAC;AACrB,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,aAAa,EAAE,IAAI;AACvB,IAAI,aAAa,EAAE,KAAK,CAAC;AACzB,IAAI,aAAa,EAAE,EAAE;AACrB,IAAI,kBAAkB,EAAE,KAAK;AAC7B,IAAI,YAAY,EAAE,KAAK;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,EAAE,MAAM,SAAS,GAAG,GAAG,EAAE,CAAC;AAC1B,EAAE,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC;AAC7B,EAAE,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC;AAC9B,EAAE,MAAM,QAAQ,GAAG,GAAG,EAAE,CAAC;AACzB,EAAE,MAAM,SAAS,GAAG,GAAG,EAAE,CAAC;AAC1B,EAAE,MAAM,SAAS,GAAG,GAAG,EAAE,CAAC;AAC1B,EAAE,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC;AAChC,EAAE,MAAM;AACR,IAAI,WAAW;AACf,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB;AACxB,IAAI,uBAAuB;AAC3B,GAAG,GAAG,cAAc,CAAC;AACrB,IAAI,gBAAgB,EAAE,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC;AACvC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACzG,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,kBAAkB,CAAC,QAAQ,EAAE;AAC7E,IAAI,QAAQ,EAAE,cAAc;AAC5B,IAAI,UAAU,GAAG;AACjB,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACtD,QAAQ,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC9B,QAAQ,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACzC,OAAO;AACP,KAAK;AACL,IAAI,UAAU,CAAC,KAAK,EAAE;AACtB,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,OAAO,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3K,KAAK;AACL,IAAI,SAAS,GAAG;AAChB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,MAAM,MAAM,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACxC,MAAM,IAAI,KAAK,CAAC,aAAa,EAAE;AAC/B,QAAQ,CAAC,EAAE,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/I,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;AACvD,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,IAAI,KAAK,CAAC,OAAO;AACrB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AACxE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAClC,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM;AACxC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,GAAG,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AACnF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,MAAM,WAAW,GAAG,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;AACxE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC;AACnE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM;AACvC,IAAI,OAAO,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACvH,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,KAAK,CAAC,SAAS,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,MAAM,CAAC,aAAa,IAAI,aAAa,CAAC,KAAK,CAAC;AACnG,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,UAAU,GAAG,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AACjG,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,aAAa,CAAC,KAAK,IAAI,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACpG,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC;AACvG,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK;AAC5B,MAAM,OAAO;AACb,IAAI,OAAO,qBAAqB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACtD,GAAG,CAAC,CAAC;AACL,EAAE,MAAMA,UAAQ,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1D,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AACzD,KAAK,MAAM;AACX,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,KAAK;AACjE,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,IAAI,UAAU,CAAC,KAAK,IAAI,eAAe,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3G,QAAQ,OAAO,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,mBAAmB,CAAC,CAAC;AAC3D,OAAO;AACP,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;AAC7B,QAAQ,OAAO,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACzD,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACnC,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AAC9D,IAAI,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;AACxD,IAAI,MAAM,mBAAmB,GAAG,KAAK,CAAC,UAAU,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC;AAC7E,IAAI,MAAM,mBAAmB,GAAG,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC;AAC7F,IAAI,MAAM,aAAa,GAAG,CAAC,CAAC,KAAK;AACjC,MAAM,IAAI,mBAAmB,IAAI,mBAAmB;AACpD,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,OAAO,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;AAC3D,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,MAAM,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK;AAC9E,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACvC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;AAC5B,QAAQ,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACvD,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,UAAU,GAAG,CAAC,IAAI,CAAC;AACnB,YAAY,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC;AACjC,YAAY,IAAI,EAAE,OAAO;AACzB,WAAW,EAAE,GAAG,QAAQ,CAAC,CAAC;AAC1B,SAAS;AACT,OAAO,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;AACtD,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvB,OAAO;AACP,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,eAAe,CAAC,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC7D,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM;AAC5C,IAAI,MAAM,QAAQ,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC/C,IAAI,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK;AAChD,MAAM,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AACrE,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,uBAAuB,GAAG,QAAQ,CAAC,MAAM;AACjD,IAAI,MAAM,QAAQ,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC/C,IAAI,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK;AACrD,MAAM,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AACrE,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1G,EAAE,MAAM,UAAU,GAAG,WAAW,EAAE,CAAC;AACnC,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,UAAU,CAAC,KAAK,KAAK,OAAO,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;AAC7F,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;AACvC,MAAM,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC;AAC7C,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,KAAK,GAAG,CAAC;AACpF,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,UAAU,CAAC,KAAK,EAAE;AAClD,MAAM,QAAQ,CAAC,MAAM;AACrB,QAAQ,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,sBAAsB,EAAE,CAAC,CAAC;AACrE,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;AAC/B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,sBAAsB,GAAG,MAAM;AACvC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACpD,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACxC,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACrD,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,KAAK,QAAQ,CAAC;AACvH,IAAI,MAAM,cAAc,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7D,IAAI,IAAI,cAAc,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI;AAC/C,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,MAAM,KAAK,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AACnD,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACjG,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACzF,IAAI,MAAM,QAAQ,GAAG,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,KAAK;AACnE,MAAM,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC1C,KAAK,EAAE,CAAC,CAAC,CAAC;AACV,IAAI,OAAO,QAAQ,GAAG,OAAO,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK;AAC3B,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC9D,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;AACjD,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAI,MAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;AACnC,IAAI,MAAM,QAAQ,GAAG,eAAe,CAAC,KAAK,IAAI,KAAK,CAAC,eAAe,KAAK,CAAC,GAAG,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,iBAAiB,GAAG,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC;AAChK,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;AACzC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM;AAC1C,IAAI,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC;AACtD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,qBAAqB,GAAG,QAAQ,CAAC,MAAM;AAC/C,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACnC,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;AACjE,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,UAAU,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AACxD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM;AAC5C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,YAAY,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,uBAAuB,CAAC,CAAC;AAC5F,IAAI,OAAO,KAAK,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC;AACxF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC;AAC3G,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;AAC1C,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE;AACvG,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACvF,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AAClG,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC9E,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AACvC,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,QAAQ,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;AACzD,KAAK;AACL,IAAI,GAAG,CAAC,GAAG,EAAE;AACb,MAAM,QAAQ,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACzB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,aAAa,CAAC;AAC5G,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACzB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC;AACvF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM;AACR,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,GAAG,GAAG,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACpC,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,IAAI,cAAc,CAAC,KAAK;AAC5B,MAAM,OAAO;AACb,IAAI,IAAI,MAAM,CAAC,kBAAkB,EAAE;AACnC,MAAM,MAAM,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACxC,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;AACvC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACzD,MAAM,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5B,KAAK;AACL,IAAI,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACvC,IAAI,QAAQ,CAAC,MAAM;AACnB,MAAM,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC3C,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,sBAAsB,GAAGC,QAAc,CAAC,aAAa,EAAED,UAAQ,CAAC,KAAK,CAAC,CAAC;AAC/E,EAAE,MAAM,iBAAiB,GAAG,CAAC,GAAG,KAAK;AACrC,IAAI,IAAI,MAAM,CAAC,aAAa,KAAK,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;AAC3D,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC;AAC/B,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AAC5D,MAAM,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC9B,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACnF,MAAM,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,kBAAkB,KAAK,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC,MAAM,EAAE;AACxG,MAAM,QAAQ,CAAC,uBAAuB,CAAC,CAAC;AACxC,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,mBAAmB,CAAC,CAAC;AACpC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,uBAAuB,GAAG,MAAM;AACxC,IAAI,MAAM,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;AACrG,IAAI,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;AACvE,IAAI,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;AACnD,IAAI,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC,KAAK,EAAE,iBAAiB,IAAI,iBAAiB,CAAC,CAAC;AACxG,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK;AAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;AACzC,MAAM,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK;AAC1B,IAAI,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;AAClC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AACpB,IAAI,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC9D,IAAI,QAAQ,CAAC,MAAM;AACnB,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACvD,QAAQ,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC3D,QAAQ,MAAM,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;AACjG,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,EAAE,eAAe,CAAC,EAAE;AAC7D,UAAU,MAAM,CAAC,aAAa,GAAG,eAAe,CAAC;AACjD,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC;AACzB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,GAAG,GAAG,EAAE,EAAE,KAAK,KAAK;AAC7C,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC1B,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;AACpC,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;AACnB,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;AAC1B,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;AACxD,QAAQ,KAAK,GAAG,CAAC,CAAC;AAClB,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,IAAI,KAAK;AAChC,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AAC7D,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,mBAAmB,EAAE,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AACjG,GAAG,CAAC;AACJ,EAAE,MAAM,sBAAsB,GAAG,MAAM;AACvC,IAAI,MAAM,CAAC,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AACnF,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,MAAM;AAC9B,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrG,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxG,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,CAAC,MAAM,KAAK;AAC/B,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,IAAI,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AACrD,MAAM,MAAM,KAAK,GAAG,aAAa,CAAC,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACrE,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACtB,QAAQ,eAAe,GAAG;AAC1B,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AAC5C,UAAU,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC7C,SAAS,CAAC;AACV,QAAQ,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC9C,QAAQ,eAAe,CAAC,MAAM,CAAC,CAAC;AAChC,OAAO,MAAM,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,eAAe,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,EAAE;AAC3F,QAAQ,eAAe,GAAG,CAAC,GAAG,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACjE,QAAQ,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1C,QAAQ,eAAe,CAAC,MAAM,CAAC,CAAC;AAChC,OAAO;AACP,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;AAC9B,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,iBAAiB,CAAC,EAAE,CAAC,CAAC;AAC9B,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;AACrD,QAAQ,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;AAC/B,OAAO;AACP,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC9C,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/B,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,MAAM,eAAe,CAAC,MAAM,CAAC,CAAC;AAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC3B,QAAQ,iBAAiB,EAAE,CAAC;AAC5B,OAAO;AACP,KAAK;AACL,IAAI,KAAK,EAAE,CAAC;AACZ,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,KAAK;AACvC,IAAI,IAAI,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AACnD,IAAI,MAAM,KAAK,GAAG,aAAa,CAAC,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AACnE,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC7C,MAAM,eAAe,GAAG;AACxB,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;AAC3C,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5C,OAAO,CAAC;AACR,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC5C,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;AAC9B,MAAM,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC3C,MAAM,eAAe,CAAC,MAAM,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;AAC5B,IAAI,KAAK,EAAE,CAAC;AACZ,GAAG,CAAC;AACJ,EAAE,MAAM,KAAK,GAAG,MAAM;AACtB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACxD,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,MAAM;AACrB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,MAAM,QAAQ,CAAC,MAAM;AACrB,QAAQ,IAAI,GAAG,CAAC;AAChB,QAAQ,OAAO,CAAC,GAAG,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AACpE,OAAO,CAAC,CAAC;AACT,MAAM,OAAO;AACb,KAAK;AACL,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AACvD,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,MAAM,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;AAC7B,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,uBAAuB,GAAG,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClK,EAAE,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK;AAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ;AACvB,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM;AACpC,MAAM,OAAO;AACb,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;AACxC,MAAM,CAAC,CAAC,cAAc,EAAE,CAAC;AACzB,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;AAChD,MAAM,MAAM,oBAAoB,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AACrE,MAAM,IAAI,oBAAoB,GAAG,CAAC;AAClC,QAAQ,OAAO;AACf,MAAM,MAAM,cAAc,GAAG,QAAQ,CAAC,oBAAoB,CAAC,CAAC;AAC5D,MAAM,QAAQ,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;AAC/C,MAAM,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;AAChE,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;AAC3D,MAAM,eAAe,CAAC,MAAM,CAAC,CAAC;AAC9B,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;AACvB,MAAM,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;AACzC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,MAAM;AAC5B,IAAI,IAAI,UAAU,CAAC;AACnB,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACnC,MAAM,UAAU,GAAG,EAAE,CAAC;AACtB,KAAK,MAAM;AACX,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC;AACtC,KAAK;AACL,IAAI,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AAC9B,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAClB,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,KAAK,EAAE,CAAC;AACZ,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,SAAS,EAAE,aAAa,GAAG,KAAK,CAAC,KAAK;AACpE,IAAI,MAAM,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC;AAC1C,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,kBAAkB,CAAC,KAAK,IAAI,WAAW,CAAC,KAAK,EAAE;AACtJ,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACzB,MAAM,OAAO,UAAU,EAAE,CAAC;AAC1B,KAAK;AACL,IAAI,IAAI,WAAW,CAAC,aAAa,CAAC,EAAE;AACpC,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;AACtB,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;AACjC,MAAM,QAAQ,GAAG,aAAa,GAAG,CAAC,CAAC;AACnC,MAAM,IAAI,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AACtC,QAAQ,QAAQ,GAAG,CAAC,CAAC;AACrB,OAAO;AACP,KAAK,MAAM,IAAI,SAAS,KAAK,UAAU,EAAE;AACzC,MAAM,QAAQ,GAAG,aAAa,GAAG,CAAC,CAAC;AACnC,MAAM,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AACtD,QAAQ,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACtC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;AACxD,MAAM,OAAO,kBAAkB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACrD,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAC;AACtC,MAAM,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACzB,MAAM,OAAO,UAAU,EAAE,CAAC;AAC1B,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;AACrF,MAAM,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;AAC5D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK;AACjC,IAAI,MAAM,CAAC,aAAa,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAClD,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACzB,MAAM,MAAM,CAAC,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK;AACvE,QAAQ,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC7E,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,UAAU,KAAK,WAAW,CAAC,UAAU,CAAC,KAAK,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACvK,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK;AAC7B,IAAI,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AAC3C,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;AACtB,MAAM,sBAAsB,EAAE,CAAC;AAC/B,KAAK,MAAM;AACX,MAAM,OAAO,aAAa,EAAE,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,KAAK,KAAK;AACxC,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,SAAS,CAAC,KAAK,EAAE;AACzB,MAAM,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACpD,MAAM,UAAU,CAAC,MAAM,CAAC,CAAC;AACzB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,MAAM;AAChC,IAAI,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;AAChC,IAAI,OAAO,QAAQ,CAAC,MAAM;AAC1B,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AAC3B,QAAQ,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAC3C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK;AAClC,IAAI,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACtC,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,aAAa,KAAK;AAC9C,IAAI,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3C,IAAI,IAAI,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;AACnD,MAAM,MAAM,EAAE,MAAM,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACnE,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,EAAE;AAC/C,MAAM,MAAM,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC;AACrG,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO;AACP,KAAK;AACL,IAAI,OAAO;AACX,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK;AACrC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK;AACrC,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,uBAAuB,GAAG,KAAK,KAAK;AAC1D,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,QAAQ,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC3D,QAAQ,MAAM,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AACxC,QAAQ,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3D,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,UAAU,EAAE;AAC9C,UAAU,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AACzD,UAAU,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AAClC,QAAQ,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC;AACtC,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,aAAa,CAAC,KAAK,EAAE;AAC/B,QAAQ,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC;AAChD,QAAQ,MAAM,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC;AAC9C,QAAQ,MAAM,iBAAiB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACjI,QAAQ,IAAI,CAAC,iBAAiB,EAAE;AAChC,UAAU,MAAM,CAAC,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC;AACtE,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,uBAAuB,EAAE;AAChE,YAAY,MAAM,CAAC,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACjE,WAAW;AACX,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,aAAa,GAAG,EAAE,CAAC;AAClC,QAAQ,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC;AACtC,OAAO;AACP,KAAK;AACL,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,mBAAmB,EAAE,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,aAAa,EAAE,MAAM;AACzC,IAAI,mBAAmB,EAAE,CAAC;AAC1B,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,GAAG,KAAK;AAC3B,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;AAC7B,QAAQ,mBAAmB,EAAE,CAAC;AAC9B,OAAO;AACP,MAAM,iBAAiB,CAAC,EAAE,CAAC,CAAC;AAC5B,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;AAC7B,MAAM,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;AAClC,MAAM,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;AACjC,MAAM,eAAe,CAAC,EAAE,CAAC,CAAC;AAC1B,KAAK;AACL,IAAI,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;AAChC,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK;AACjD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;AAChE,IAAI,IAAI,UAAU,IAAI,KAAK,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;AACrK,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC,aAAa,EAAE;AACtD,MAAM,CAAC,EAAE,GAAG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/I,KAAK;AACL,GAAG,EAAE;AACL,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,MAAM;AACnC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACjC,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,aAAa,KAAK,KAAK,EAAE;AAC7D,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK;AACL,GAAG,EAAE;AACL,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,KAAK,EAAE,MAAM;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,eAAe,CAAC,KAAK,EAAE,MAAM;AAC3C,IAAI,mBAAmB,EAAE,CAAC;AAC1B,IAAI,OAAO,OAAO,CAAC,KAAK,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACnE,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,IAAI,MAAM,CAAC,YAAY;AAC3B,MAAM,OAAO;AACb,IAAI,aAAa,EAAE,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;AACxC,IAAI,MAAM,cAAc,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACrD,IAAI,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;AAChC,MAAM,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACzC,MAAM,IAAI,CAAC,GAAG,WAAW,CAAC;AAC1B,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;AACvB,QAAQ,CAAC,GAAG,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACvC,OAAO;AACP,MAAM,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;AACjC,QAAQ,SAAS,CAAC,YAAY,EAAE,CAAC,kGAAkG,CAAC,CAAC,CAAC;AACtI,QAAQ,MAAM;AACd,OAAO,MAAM;AACb,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACpC,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,UAAU,EAAE,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,iBAAiB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AAC7C,EAAE,iBAAiB,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;AACvD,EAAE,iBAAiB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AAC5C,EAAE,iBAAiB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;AAC/C,EAAE,iBAAiB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;AAClD,EAAE,iBAAiB,CAAC,eAAe,EAAE,sBAAsB,CAAC,CAAC;AAC7D,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,eAAe;AACnB,IAAI,kBAAkB;AACtB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,WAAW;AACf,cAAIA,UAAQ;AACZ,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,IAAI,UAAU;AACd,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,qBAAqB;AACzB,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,sBAAsB;AAC1B,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,kBAAkB;AACtB,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,IAAI,QAAQ,EAAE,YAAY;AAC1B,IAAI,OAAO;AACX,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,OAAO,EAAE,aAAa;AAC1B,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB;AACxB,IAAI,uBAAuB;AAC3B,GAAG,CAAC;AACJ;;;;"}